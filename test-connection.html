<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Supabase Connection Test</title>
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
</head>
<body>
    <h1>Supabase Connection Test</h1>
    <div id="result"></div>
    
    <script>
        const SUPABASE_URL = 'https://niviihlfsqocuboafudh.supabase.co';
        const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.p1kZQezwzr_7ZHs5Nd8sHZouoY76MmfnHSedeRi7gSc';
        
        const supabase = window.supabase.createClient(SUPABASE_URL, SUPABASE_ANON_KEY);
        
        async function testConnection() {
            const resultDiv = document.getElementById('result');
            
            try {
                console.log('Testing connection...');
                
                // Test 1: Simple query
                const { data: industries, error: industriesError } = await supabase
                    .from('industries')
                    .select('*')
                    .limit(5);
                
                if (industriesError) {
                    throw new Error(`Industries query failed: ${industriesError.message}`);
                }
                
                // Test 2: Startup ideas query
                const { data: ideas, error: ideasError } = await supabase
                    .from('startup_ideas')
                    .select('*')
                    .limit(3);
                
                if (ideasError) {
                    throw new Error(`Ideas query failed: ${ideasError.message}`);
                }
                
                resultDiv.innerHTML = `
                    <h2 style="color: green;">✅ Connection Successful!</h2>
                    <p><strong>Industries found:</strong> ${industries.length}</p>
                    <p><strong>Ideas found:</strong> ${ideas.length}</p>
                    <h3>Sample Industries:</h3>
                    <ul>
                        ${industries.map(industry => `<li>${industry.name}</li>`).join('')}
                    </ul>
                    <h3>Sample Ideas:</h3>
                    <ul>
                        ${ideas.map(idea => `<li>${idea.title}</li>`).join('')}
                    </ul>
                `;
                
            } catch (error) {
                console.error('Connection test failed:', error);
                resultDiv.innerHTML = `
                    <h2 style="color: red;">❌ Connection Failed!</h2>
                    <p><strong>Error:</strong> ${error.message}</p>
                    <p><strong>URL:</strong> ${SUPABASE_URL}</p>
                    <p><strong>Key (first 20 chars):</strong> ${SUPABASE_ANON_KEY.substring(0, 20)}...</p>
                `;
            }
        }
        
        // Run test when page loads
        testConnection();
    </script>
</body>
</html>
