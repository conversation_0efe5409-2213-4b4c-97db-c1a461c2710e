import "jsr:@supabase/functions-js/edge-runtime.d.ts";
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';
import Stripe from 'https://esm.sh/stripe@14.21.0?target=deno';

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

interface PaymentRequest {
  priceId: string;
  successUrl: string;
  cancelUrl: string;
}

Deno.serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    // Initialize Stripe
    const stripe = new Stripe(Deno.env.get('STRIPE_SECRET_KEY') || '', {
      apiVersion: '2023-10-16',
    });

    // Initialize Supabase client
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    );

    // Get user from JWT token
    const authHeader = req.headers.get('Authorization');
    if (!authHeader) {
      throw new Error('No authorization header');
    }

    const token = authHeader.replace('Bearer ', '');
    const { data: { user }, error: userError } = await supabaseClient.auth.getUser(token);

    if (userError || !user) {
      throw new Error('Invalid user token');
    }

    // Parse request body
    const { priceId, successUrl, cancelUrl }: PaymentRequest = await req.json();

    if (!priceId) {
      throw new Error('Price ID is required');
    }

    console.log(`Creating payment intent for user ${user.id} with price ${priceId}`);

    // Check if user already has a Stripe customer
    const { data: existingProfile } = await supabaseClient
      .from('user_profiles')
      .select('stripe_customer_id')
      .eq('id', user.id)
      .single();

    let customerId = existingProfile?.stripe_customer_id;

    // Validate existing customer ID or create new one
    if (customerId) {
      try {
        // Verify the customer exists in the current Stripe environment
        await stripe.customers.retrieve(customerId);
        console.log(`Using existing Stripe customer: ${customerId}`);
      } catch (error) {
        console.log(`Existing customer ID ${customerId} is invalid (likely from test mode), creating new customer`);
        customerId = null; // Reset to create new customer
      }
    }

    // Create Stripe customer if doesn't exist or is invalid
    if (!customerId) {
      console.log('Creating new Stripe customer');
      const customer = await stripe.customers.create({
        email: user.email,
        metadata: {
          supabase_user_id: user.id,
        },
      });
      customerId = customer.id;

      // Update user profile with new Stripe customer ID
      await supabaseClient
        .from('user_profiles')
        .upsert({
          id: user.id,
          email: user.email || '',
          stripe_customer_id: customerId,
          subscription_status: 'free',
        });
    }

    // Create payment intent
    const discountPriceId = Deno.env.get('STRIPE_PRICE_ID_DISCOUNT');
    const originalPriceId = Deno.env.get('STRIPE_PRICE_ID_ORIGINAL');
    
    // Determine amount based on exact price ID match
    let amount: number;
    if (priceId === discountPriceId) {
      amount = 499; // $4.99 in cents
    } else if (priceId === originalPriceId) {
      amount = 999; // $9.99 in cents
    } else {
      // Fallback: check if price ID contains 'discount' keyword
      amount = priceId.includes('discount') ? 499 : 999;
      console.warn(`Price ID ${priceId} doesn't match configured environment variables. Using fallback logic.`);
    }

    console.log(`Creating payment intent for ${amount} cents (${amount/100} USD) with price ID: ${priceId}`);

    const paymentIntent = await stripe.paymentIntents.create({
      amount: amount,
      currency: 'usd',
      customer: customerId,
      metadata: {
        supabase_user_id: user.id,
        price_id: priceId,
        amount_usd: (amount / 100).toString(), // Store USD amount for reference
      },
      automatic_payment_methods: {
        enabled: true,
      },
    });

    console.log(`Payment intent created: ${paymentIntent.id}`);

    // Update user profile with payment intent ID
    await supabaseClient
      .from('user_profiles')
      .update({
        payment_intent_id: paymentIntent.id,
        updated_at: new Date().toISOString(),
      })
      .eq('id', user.id);

    return new Response(
      JSON.stringify({
        paymentIntent: {
          id: paymentIntent.id,
          clientSecret: paymentIntent.client_secret,
          amount: paymentIntent.amount,
          currency: paymentIntent.currency,
          status: paymentIntent.status,
        },
        customer: customerId,
        publishableKey: Deno.env.get('STRIPE_PUBLISHABLE_KEY'),
      }),
      {
        status: 200,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      }
    );
  } catch (error) {
    console.error('Stripe payment error:', error);

    // Provide more specific error messages
    let errorMessage = error.message;
    let statusCode = 400;

    if (error.message.includes('No such customer')) {
      errorMessage = 'Customer validation failed. Please try again.';
      statusCode = 400;
    } else if (error.message.includes('No such payment_intent')) {
      errorMessage = 'Payment intent validation failed. Please try again.';
      statusCode = 400;
    } else if (error.message.includes('test mode') || error.message.includes('live mode')) {
      errorMessage = 'Payment configuration error. Please contact support.';
      statusCode = 500;
    }

    return new Response(
      JSON.stringify({
        error: errorMessage,
        details: process.env.NODE_ENV === 'development' ? error.message : undefined,
      }),
      {
        status: statusCode,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      }
    );
  }
});
