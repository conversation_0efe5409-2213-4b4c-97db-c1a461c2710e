import React, { useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'wouter';
import { Arrow<PERSON><PERSON>t, Clock, Eye, Share2, Calendar, Tag } from 'lucide-react';
import { useBlogPost, incrementBlogPostViews } from '@/hooks/use-blog';
import SEOHead from '@/components/seo-head';
import RelatedArticles from '@/components/related-articles';
import BlogSocialShare from '@/components/blog-social-share';
import TableOfContents from '@/components/table-of-contents';
import ReadingProgress from '@/components/reading-progress';
import { BlogBreadcrumbs } from '@/components/breadcrumbs';
import PerformanceMonitor from '@/components/performance-monitor';
import BlogComments from '@/components/blog-comments';
import { formatDistanceToNow } from 'date-fns';

export default function BlogPost() {
  const { slug } = useParams();
  const { data: post, isLoading, error } = useBlogPost(slug!);

  useEffect(() => {
    if (post) {
      incrementBlogPostViews(post.id);
    }
  }, [post]);

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-400 mx-auto"></div>
          <p className="mt-4 text-gray-300">Loading article...</p>
        </div>
      </div>
    );
  }

  if (error || !post) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-white mb-4">Article Not Found</h1>
          <p className="text-gray-300 mb-6">The article you're looking for doesn't exist.</p>
          <Link href="/blog" className="text-blue-400 hover:text-blue-300">
            ← Back to Blog
          </Link>
        </div>
      </div>
    );
  }

  return (
    <>
      <PerformanceMonitor />

      <SEOHead
        title={`${post.title} | IdeaHunter Blog`}
        description={post.meta_description || post.excerpt}
        keywords={post.keywords || []}
        url={`https://ideahunter.today/blog/${post.slug}`}
        type="article"
        structuredDataType="Article"
        article={{
          author: post.author,
          publishedTime: post.published_at,
          modifiedTime: post.updated_at,
          section: post.category,
          tags: post.tags || []
        }}
        breadcrumbs={[
          { name: 'Home', url: 'https://ideahunter.today' },
          { name: 'Blog', url: 'https://ideahunter.today/blog' },
          { name: post.title, url: `https://ideahunter.today/blog/${post.slug}` }
        ]}
      />

      <ReadingProgress />

      <div className="min-h-screen bg-gray-900">
        <div className="max-w-7xl mx-auto px-4 py-8">
          <div className="flex flex-col lg:flex-row gap-8">
            {/* Main Content */}
            <div className="flex-1 lg:max-w-4xl">
              {/* Breadcrumbs */}
              <BlogBreadcrumbs
                category={post.category}
                title={post.title}
                className="mb-4"
              />

              {/* Back button */}
              <Link href="/blog" className="inline-flex items-center text-blue-400 hover:text-blue-300 mb-8">
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Blog
              </Link>

              {/* Article header */}
              <article className="bg-gray-800/50 border border-gray-700 rounded-lg shadow-xl overflow-hidden">
                {post.featured_image_url && (
                  <img 
                    src={post.featured_image_url} 
                    alt={post.title}
                    className="w-full h-64 object-cover"
                  />
                )}
                
                <div className="p-8">
                  {/* Article meta */}
                  <div className="flex items-center text-sm text-gray-400 mb-4">
                    <Calendar className="w-4 h-4 mr-2" />
                    <span>{formatDistanceToNow(new Date(post.published_at))} ago</span>
                    <span className="mx-2">•</span>
                    <Clock className="w-4 h-4 mr-2" />
                    <span>{post.reading_time} min read</span>
                    <span className="mx-2">•</span>
                    <Eye className="w-4 h-4 mr-2" />
                    <span>{post.view_count} views</span>
                  </div>

                  {/* Title */}
                  <h1 className="text-4xl font-bold text-white mb-6">{post.title}</h1>
                  
                  {/* Excerpt */}
                  {post.excerpt && (
                    <p className="text-xl text-gray-300 mb-8 leading-relaxed">{post.excerpt}</p>
                  )}

                  {/* Article content */}
                  <div 
                    className="prose prose-lg prose-invert max-w-none prose-headings:text-white prose-p:text-gray-300 prose-a:text-blue-400 prose-strong:text-white prose-code:text-blue-300 prose-blockquote:text-gray-300 prose-blockquote:border-gray-600"
                    dangerouslySetInnerHTML={{ __html: post.content }}
                  />

                  {/* Article footer */}
                  <div className="mt-12 pt-8 border-t border-gray-700">
                    <div className="flex items-center justify-between flex-wrap gap-4">
                      {/* Stats */}
                      <div className="flex items-center space-x-6 text-sm text-gray-400">
                        <div className="flex items-center">
                          <Eye className="w-4 h-4 mr-2" />
                          {post.view_count} views
                        </div>
                        <div className="flex items-center">
                          <Share2 className="w-4 h-4 mr-2" />
                          {post.share_count} shares
                        </div>
                        
                        {/* Social Share */}
                        <div className="ml-auto">
                          <BlogSocialShare
                            url={`https://ideahunter.today/blog/${post.slug}`}
                            title={post.title}
                            description={post.excerpt}
                          />
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Tags */}
                  {post.tags && post.tags.length > 0 && (
                    <div className="mt-6">
                      <div className="flex items-center flex-wrap gap-2">
                        <Tag className="w-4 h-4 text-gray-400" />
                        {post.tags.map((tag, index) => (
                          <span
                            key={index}
                            className="bg-gray-700 text-gray-300 text-sm px-3 py-1 rounded-full hover:bg-gray-600 transition-colors"
                          >
                            {tag}
                          </span>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </article>

              {/* Comments Section */}
              <BlogComments
                postSlug={post.slug}
                postTitle={post.title}
              />
            </div>

            {/* Sidebar */}
            <div className="lg:w-80 lg:flex-shrink-0">
              <div className="sticky top-8 space-y-6">
                {/* Table of Contents */}
                <TableOfContents content={post.content} />

                {/* Social Share */}
                <BlogSocialShare
                  url={`https://ideahunter.today/blog/${post.slug}`}
                  title={post.title}
                  description={post.excerpt}
                />

                {/* Related Articles */}
                <RelatedArticles
                  currentPostId={post.id}
                  tags={post.tags || []}
                  category={post.category}
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
