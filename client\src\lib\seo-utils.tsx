// SEO utility functions for content optimization

export interface SEOAnalysis {
  titleLength: number;
  titleOptimal: boolean;
  descriptionLength: number;
  descriptionOptimal: boolean;
  keywordDensity: number;
  keywordOptimal: boolean;
  headingStructure: HeadingAnalysis;
  readabilityScore: number;
  internalLinks: number;
  externalLinks: number;
  imageAltTags: number;
  missingAltTags: number;
}

export interface HeadingAnalysis {
  h1Count: number;
  h2Count: number;
  h3Count: number;
  h4Count: number;
  h5Count: number;
  h6Count: number;
  hasProperStructure: boolean;
  issues: string[];
}

// Analyze SEO metrics for content
export function analyzeSEO(
  title: string,
  description: string,
  content: string,
  targetKeyword: string
): SEOAnalysis {
  const titleLength = title.length;
  const descriptionLength = description.length;
  
  // Calculate keyword density
  const wordCount = content.split(/\s+/).length;
  const keywordMatches = (content.toLowerCase().match(new RegExp(targetKeyword.toLowerCase(), 'g')) || []).length;
  const keywordDensity = (keywordMatches / wordCount) * 100;
  
  // Analyze heading structure
  const headingStructure = analyzeHeadingStructure(content);
  
  // Calculate readability score (simplified Flesch Reading Ease)
  const readabilityScore = calculateReadabilityScore(content);
  
  // Count links
  const internalLinks = (content.match(/href="\/[^"]*"/g) || []).length;
  const externalLinks = (content.match(/href="https?:\/\/[^"]*"/g) || []).length;
  
  // Count images and alt tags
  const images = content.match(/<img[^>]*>/g) || [];
  const imageAltTags = (content.match(/<img[^>]*alt="[^"]*"[^>]*>/g) || []).length;
  const missingAltTags = images.length - imageAltTags;
  
  return {
    titleLength,
    titleOptimal: titleLength >= 30 && titleLength <= 60,
    descriptionLength,
    descriptionOptimal: descriptionLength >= 120 && descriptionLength <= 155,
    keywordDensity,
    keywordOptimal: keywordDensity >= 1 && keywordDensity <= 2,
    headingStructure,
    readabilityScore,
    internalLinks,
    externalLinks,
    imageAltTags,
    missingAltTags
  };
}

// Analyze heading structure
function analyzeHeadingStructure(content: string): HeadingAnalysis {
  const h1Count = (content.match(/<h1[^>]*>/g) || []).length;
  const h2Count = (content.match(/<h2[^>]*>/g) || []).length;
  const h3Count = (content.match(/<h3[^>]*>/g) || []).length;
  const h4Count = (content.match(/<h4[^>]*>/g) || []).length;
  const h5Count = (content.match(/<h5[^>]*>/g) || []).length;
  const h6Count = (content.match(/<h6[^>]*>/g) || []).length;
  
  const issues: string[] = [];
  
  // Check for proper structure
  if (h1Count === 0) {
    issues.push('Missing H1 tag');
  } else if (h1Count > 1) {
    issues.push('Multiple H1 tags found');
  }
  
  if (h2Count === 0 && content.length > 1000) {
    issues.push('Long content without H2 subheadings');
  }
  
  const hasProperStructure = issues.length === 0;
  
  return {
    h1Count,
    h2Count,
    h3Count,
    h4Count,
    h5Count,
    h6Count,
    hasProperStructure,
    issues
  };
}

// Calculate readability score (simplified)
function calculateReadabilityScore(content: string): number {
  const sentences = content.split(/[.!?]+/).length;
  const words = content.split(/\s+/).length;
  const syllables = estimateSyllables(content);
  
  // Simplified Flesch Reading Ease formula
  const score = 206.835 - (1.015 * (words / sentences)) - (84.6 * (syllables / words));
  
  return Math.max(0, Math.min(100, score));
}

// Estimate syllable count
function estimateSyllables(text: string): number {
  const words = text.toLowerCase().split(/\s+/);
  let syllableCount = 0;
  
  words.forEach(word => {
    // Remove punctuation
    word = word.replace(/[^a-z]/g, '');
    if (word.length === 0) return;
    
    // Count vowel groups
    const vowelGroups = word.match(/[aeiouy]+/g) || [];
    syllableCount += vowelGroups.length;
    
    // Adjust for silent e
    if (word.endsWith('e')) {
      syllableCount -= 1;
    }
    
    // Minimum one syllable per word
    if (syllableCount === 0) {
      syllableCount = 1;
    }
  });
  
  return syllableCount;
}

// Generate SEO-optimized title suggestions
export function generateTitleSuggestions(
  baseTitle: string,
  targetKeyword: string,
  category: string
): string[] {
  const suggestions = [
    `${targetKeyword}: ${baseTitle}`,
    `${baseTitle} | ${category} Guide`,
    `Complete Guide to ${baseTitle}`,
    `${baseTitle} - ${category} Tips & Strategies`,
    `How to ${baseTitle} in 2025`,
    `${baseTitle}: ${category} Best Practices`,
    `Ultimate ${baseTitle} Guide for ${category}`,
    `${baseTitle} - Everything You Need to Know`
  ];
  
  return suggestions.filter(title => title.length <= 60);
}

// Generate meta description suggestions
export function generateMetaDescriptions(
  title: string,
  content: string,
  targetKeyword: string
): string[] {
  const firstSentence = content.split('.')[0];
  const excerpt = content.substring(0, 150);
  
  const suggestions = [
    `${firstSentence}. Learn more about ${targetKeyword} and best practices.`,
    `Discover ${targetKeyword} strategies and tips. ${excerpt}...`,
    `Complete guide to ${targetKeyword}. Expert insights and actionable advice.`,
    `Learn ${targetKeyword} from experts. Step-by-step guide with real examples.`,
    `${targetKeyword} explained: ${excerpt}...`
  ];
  
  return suggestions.filter(desc => desc.length <= 155);
}

// Extract and suggest LSI keywords
export function generateLSIKeywords(targetKeyword: string, category: string): string[] {
  const lsiKeywords: Record<string, string[]> = {
    'startup': ['business idea', 'entrepreneur', 'venture', 'innovation', 'funding'],
    'reddit': ['community', 'discussion', 'social media', 'forum', 'user-generated'],
    'validation': ['testing', 'proof of concept', 'market research', 'feedback', 'verification'],
    'analysis': ['data', 'research', 'insights', 'metrics', 'evaluation'],
    'guide': ['tutorial', 'how-to', 'step-by-step', 'instructions', 'manual'],
    'case study': ['example', 'success story', 'real-world', 'implementation', 'results']
  };
  
  const keywords = new Set<string>();
  
  // Add category-specific keywords
  if (lsiKeywords[category.toLowerCase()]) {
    lsiKeywords[category.toLowerCase()].forEach(kw => keywords.add(kw));
  }
  
  // Add target keyword variations
  const keywordParts = targetKeyword.split(' ');
  keywordParts.forEach(part => {
    if (lsiKeywords[part.toLowerCase()]) {
      lsiKeywords[part.toLowerCase()].forEach(kw => keywords.add(kw));
    }
  });
  
  return Array.from(keywords);
}

// Check content for SEO issues
export function checkSEOIssues(analysis: SEOAnalysis): string[] {
  const issues: string[] = [];
  
  if (!analysis.titleOptimal) {
    if (analysis.titleLength < 30) {
      issues.push('Title is too short (should be 30-60 characters)');
    } else {
      issues.push('Title is too long (should be 30-60 characters)');
    }
  }
  
  if (!analysis.descriptionOptimal) {
    if (analysis.descriptionLength < 120) {
      issues.push('Meta description is too short (should be 120-155 characters)');
    } else {
      issues.push('Meta description is too long (should be 120-155 characters)');
    }
  }
  
  if (!analysis.keywordOptimal) {
    if (analysis.keywordDensity < 1) {
      issues.push('Keyword density is too low (should be 1-2%)');
    } else {
      issues.push('Keyword density is too high (should be 1-2%)');
    }
  }
  
  if (analysis.internalLinks < 3) {
    issues.push('Add more internal links (recommended: 3-5 per article)');
  }
  
  if (analysis.missingAltTags > 0) {
    issues.push(`${analysis.missingAltTags} images missing alt tags`);
  }
  
  if (analysis.readabilityScore < 60) {
    issues.push('Content readability could be improved (use shorter sentences and simpler words)');
  }
  
  issues.push(...analysis.headingStructure.issues);
  
  return issues;
}

// Generate SEO score
export function calculateSEOScore(analysis: SEOAnalysis): number {
  let score = 0;
  let maxScore = 0;
  
  // Title optimization (20 points)
  maxScore += 20;
  if (analysis.titleOptimal) score += 20;
  else if (analysis.titleLength >= 25 && analysis.titleLength <= 65) score += 10;
  
  // Description optimization (20 points)
  maxScore += 20;
  if (analysis.descriptionOptimal) score += 20;
  else if (analysis.descriptionLength >= 100 && analysis.descriptionLength <= 170) score += 10;
  
  // Keyword density (15 points)
  maxScore += 15;
  if (analysis.keywordOptimal) score += 15;
  else if (analysis.keywordDensity >= 0.5 && analysis.keywordDensity <= 3) score += 8;
  
  // Heading structure (15 points)
  maxScore += 15;
  if (analysis.headingStructure.hasProperStructure) score += 15;
  else if (analysis.headingStructure.h1Count === 1) score += 8;
  
  // Internal links (10 points)
  maxScore += 10;
  if (analysis.internalLinks >= 3) score += 10;
  else if (analysis.internalLinks >= 1) score += 5;
  
  // Image optimization (10 points)
  maxScore += 10;
  if (analysis.missingAltTags === 0) score += 10;
  else if (analysis.missingAltTags <= 2) score += 5;
  
  // Readability (10 points)
  maxScore += 10;
  if (analysis.readabilityScore >= 70) score += 10;
  else if (analysis.readabilityScore >= 50) score += 5;
  
  return Math.round((score / maxScore) * 100);
}
