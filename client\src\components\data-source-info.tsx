import React from 'react';

interface DataSourceInfoProps {
  collectionDate?: string;
  dataVolume?: string;
  communityCount?: string;
  updateFrequency?: string;
  limitations?: string[];
  className?: string;
}

export const DataSourceInfo: React.FC<DataSourceInfoProps> = ({
  collectionDate = "2024年8月2日",
  dataVolume = "10,247个Reddit帖子",
  communityCount = "35个相关subreddit",
  updateFrequency = "每周更新",
  limitations = ["仅包含英文内容", "排除删除帖子", "基于公开可用数据"],
  className = ""
}) => {
  return (
    <div className={`bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6 ${className}`}>
      <h4 className="font-semibold text-blue-900 mb-2 flex items-center">
        <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
        </svg>
        📊 数据来源
      </h4>
      <ul className="text-sm text-blue-800 space-y-1">
        <li className="flex items-start">
          <span className="font-medium mr-2">收集时间:</span>
          <span>{collectionDate}</span>
        </li>
        <li className="flex items-start">
          <span className="font-medium mr-2">数据量:</span>
          <span>{dataVolume}</span>
        </li>
        <li className="flex items-start">
          <span className="font-medium mr-2">社区数量:</span>
          <span>{communityCount}</span>
        </li>
        <li className="flex items-start">
          <span className="font-medium mr-2">更新频率:</span>
          <span>{updateFrequency}</span>
        </li>
        <li className="flex items-start">
          <span className="font-medium mr-2">数据限制:</span>
          <span>{limitations.join("，")}</span>
        </li>
      </ul>
      
      <div className="mt-3 pt-3 border-t border-blue-200">
        <p className="text-xs text-blue-700">
          <span className="font-medium">数据质量保证:</span> 
          所有数据经过人工审核和AI算法验证，确保内容质量和相关性。
        </p>
      </div>
    </div>
  );
};

// 专门用于不同页面类型的数据源组件
export const IdeaDataSourceInfo: React.FC<{ className?: string }> = ({ className }) => (
  <DataSourceInfo
    collectionDate="2024年8月2日"
    dataVolume="10,247个Reddit帖子"
    communityCount="35个创业相关subreddit"
    updateFrequency="每周更新"
    limitations={[
      "仅包含英文内容",
      "排除已删除或被移除的帖子",
      "基于Reddit公开API数据",
      "不包含私人或付费社区内容"
    ]}
    className={className}
  />
);

export const IndustryDataSourceInfo: React.FC<{ industry?: string; className?: string }> = ({ 
  industry = "行业", 
  className 
}) => (
  <DataSourceInfo
    collectionDate="2024年8月2日"
    dataVolume={`${industry}相关的1,200+个Reddit帖子`}
    communityCount="15个专业subreddit"
    updateFrequency="每周更新"
    limitations={[
      "专注于英文讨论",
      "基于社区投票和互动数据",
      "排除低质量或垃圾内容",
      "数据经过行业专家验证"
    ]}
    className={className}
  />
);

export const BlogDataSourceInfo: React.FC<{ className?: string }> = ({ className }) => (
  <DataSourceInfo
    collectionDate="2024年8月2日"
    dataVolume="560个经过验证的创业想法"
    communityCount="35个Reddit社区"
    updateFrequency="每周分析更新"
    limitations={[
      "基于Reddit社区讨论",
      "人工筛选和分析",
      "专注于可执行的商业想法",
      "排除理论性或不切实际的概念"
    ]}
    className={className}
  />
);

export default DataSourceInfo;
