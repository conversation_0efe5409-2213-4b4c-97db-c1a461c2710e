import { useState } from "react";
import { useLocation } from "wouter";
import { motion } from "framer-motion";
import { ArrowLeft, CheckCircle, Target, Users, TrendingUp, Lightbulb, AlertCircle, ExternalLink, Clock, DollarSign } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import SEOHead from "@/components/seo-head";
import ParticleBackground from "@/components/particle-background";

interface ValidationStep {
  id: string;
  title: string;
  description: string;
  timeframe: string;
  cost: string;
  difficulty: "Easy" | "Medium" | "Hard";
  methods: string[];
  tips: string[];
  redFlags: string[];
}

const VALIDATION_STEPS: ValidationStep[] = [
  {
    id: "problem-research",
    title: "1. Problem Research & Market Analysis",
    description: "Understand the problem deeply and validate that it's worth solving",
    timeframe: "1-2 weeks",
    cost: "$0-100",
    difficulty: "Easy",
    methods: [
      "Reddit discussion analysis (use IdeaHunter insights)",
      "Google Trends research",
      "Social media listening",
      "Industry reports and surveys",
      "Competitor analysis"
    ],
    tips: [
      "Look for recurring complaints and pain points",
      "Check if people are already paying for solutions",
      "Analyze comment sentiment on Reddit posts",
      "Use IdeaHunter's existing solutions analysis"
    ],
    redFlags: [
      "No one is talking about the problem",
      "Existing solutions are highly rated",
      "Problem only affects a tiny niche",
      "People say they want it but don't pay for solutions"
    ]
  },
  {
    id: "customer-interviews",
    title: "2. Customer Discovery Interviews",
    description: "Talk directly to potential customers to validate assumptions",
    timeframe: "2-3 weeks",
    cost: "$0-200",
    difficulty: "Medium",
    methods: [
      "1-on-1 interviews with target customers",
      "Online surveys and forms",
      "Focus groups",
      "Reddit AMA or discussion posts",
      "Social media outreach"
    ],
    tips: [
      "Ask about their current workflow and pain points",
      "Focus on their behavior, not opinions",
      "Ask 'How do you currently solve this problem?'",
      "Listen for emotional language about frustrations"
    ],
    redFlags: [
      "People say 'nice idea' but can't describe the problem",
      "They're satisfied with current solutions",
      "They wouldn't pay for a solution",
      "Problem isn't urgent or frequent enough"
    ]
  },
  {
    id: "mvp-testing",
    title: "3. MVP Development & Testing",
    description: "Build a minimal version to test core assumptions",
    timeframe: "2-6 weeks",
    cost: "$100-2000",
    difficulty: "Hard",
    methods: [
      "Landing page with signup form",
      "Prototype or wireframe testing",
      "Concierge MVP (manual service)",
      "Wizard of Oz testing",
      "Beta version with core features"
    ],
    tips: [
      "Focus on one core feature that solves the main problem",
      "Use no-code tools to build quickly",
      "Measure engagement, not just signups",
      "Get feedback on every interaction"
    ],
    redFlags: [
      "Low conversion rates on landing page",
      "Users don't return after first use",
      "People sign up but don't engage",
      "Feedback is lukewarm or confused"
    ]
  },
  {
    id: "market-validation",
    title: "4. Market Size & Business Model Validation",
    description: "Validate that there's a viable business opportunity",
    timeframe: "1-2 weeks",
    cost: "$0-500",
    difficulty: "Medium",
    methods: [
      "TAM/SAM/SOM analysis",
      "Pricing research and testing",
      "Revenue model validation",
      "Customer acquisition cost analysis",
      "Competitive pricing analysis"
    ],
    tips: [
      "Look at how competitors price their solutions",
      "Test different pricing models with potential customers",
      "Calculate customer lifetime value",
      "Understand customer acquisition channels"
    ],
    redFlags: [
      "Market is too small to sustain a business",
      "Customer acquisition costs are too high",
      "Customers won't pay enough to make it profitable",
      "Too much competition with better solutions"
    ]
  }
];

const VALIDATION_CHECKLIST = [
  "Problem is clearly defined and urgent",
  "Target customers actively seek solutions",
  "Market size is large enough for your goals",
  "You can reach customers cost-effectively",
  "Customers are willing to pay for a solution",
  "You can build a solution better than existing ones",
  "Business model is sustainable and profitable",
  "You have the skills/resources to execute"
];

export default function StartupValidationGuide() {
  const [, setLocation] = useLocation();
  const [completedSteps, setCompletedSteps] = useState<string[]>([]);
  const [checkedItems, setCheckedItems] = useState<number[]>([]);

  const toggleStepCompletion = (stepId: string) => {
    setCompletedSteps(prev => 
      prev.includes(stepId) 
        ? prev.filter(id => id !== stepId)
        : [...prev, stepId]
    );
  };

  const toggleChecklistItem = (index: number) => {
    setCheckedItems(prev => 
      prev.includes(index) 
        ? prev.filter(i => i !== index)
        : [...prev, index]
    );
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case "Easy": return "bg-green-500/20 text-green-400 border-green-500/30";
      case "Medium": return "bg-yellow-500/20 text-yellow-400 border-yellow-500/30";
      case "Hard": return "bg-red-500/20 text-red-400 border-red-500/30";
      default: return "bg-gray-500/20 text-gray-400 border-gray-500/30";
    }
  };

  const progressPercentage = (completedSteps.length / VALIDATION_STEPS.length) * 100;
  const checklistProgress = (checkedItems.length / VALIDATION_CHECKLIST.length) * 100;

  return (
    <>
      <SEOHead 
        title="How to Validate Startup Ideas: Complete Guide 2025 | IdeaHunter"
        description="Learn how to validate startup ideas with our comprehensive step-by-step guide. From problem research to MVP testing, discover proven methods to validate business opportunities before investing time and money."
        keywords={[
          "startup idea validation",
          "how to validate startup ideas",
          "startup validation guide",
          "business idea validation",
          "MVP testing",
          "customer discovery",
          "startup validation methods",
          "business validation checklist"
        ]}
        type="article"
        structuredData={{
          "@context": "https://schema.org",
          "@type": "HowTo",
          "name": "How to Validate Startup Ideas",
          "description": "Complete guide to validating startup ideas before building a business",
          "step": VALIDATION_STEPS.map((step, index) => ({
            "@type": "HowToStep",
            "position": index + 1,
            "name": step.title,
            "text": step.description
          }))
        }}
      />
      
      <div className="bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 relative overflow-hidden min-h-screen">
        <ParticleBackground />
        
        <div className="relative z-10 container mx-auto px-4 py-8">
          <div className="max-w-4xl mx-auto">
            
            {/* Header */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="mb-8"
            >
              <Button
                onClick={() => setLocation('/faq')}
                variant="ghost"
                className="mb-6 text-gray-400 hover:text-white transition-colors"
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to FAQ
              </Button>

              <div className="flex items-center space-x-4 mb-6">
                <div className="w-16 h-16 rounded-xl bg-gradient-to-r from-green-500 to-blue-600 flex items-center justify-center">
                  <Target className="w-8 h-8 text-white" />
                </div>
                <div>
                  <h1 className="text-4xl md:text-5xl font-bold text-white mb-2">
                    How to Validate Startup Ideas
                  </h1>
                  <p className="text-xl text-gray-300">
                    Complete Step-by-Step Validation Guide
                  </p>
                </div>
              </div>

              <p className="text-lg text-gray-300 leading-relaxed">
                Before investing months building a product, validate your startup idea with these proven methods. 
                This guide will help you avoid common pitfalls and increase your chances of building something people actually want.
              </p>
            </motion.div>

            {/* Progress Overview */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 }}
              className="mb-8"
            >
              <Card className="glass-card border-white/10">
                <CardHeader>
                  <CardTitle className="text-white flex items-center">
                    <TrendingUp className="w-5 h-5 mr-2 text-green-400" />
                    Your Validation Progress
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div>
                      <div className="flex justify-between text-sm mb-2">
                        <span className="text-gray-300">Validation Steps</span>
                        <span className="text-white">{completedSteps.length}/{VALIDATION_STEPS.length} completed</span>
                      </div>
                      <Progress value={progressPercentage} className="h-2" />
                    </div>
                    <div>
                      <div className="flex justify-between text-sm mb-2">
                        <span className="text-gray-300">Validation Checklist</span>
                        <span className="text-white">{checkedItems.length}/{VALIDATION_CHECKLIST.length} checked</span>
                      </div>
                      <Progress value={checklistProgress} className="h-2" />
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>

            {/* Validation Steps */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
              className="space-y-6 mb-8"
            >
              {VALIDATION_STEPS.map((step, index) => (
                <Card 
                  key={step.id} 
                  className={`glass-card border-white/10 ${
                    completedSteps.includes(step.id) ? 'border-green-500/30 bg-green-500/5' : ''
                  }`}
                >
                  <CardHeader>
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-3 mb-3">
                          <Button
                            size="sm"
                            variant={completedSteps.includes(step.id) ? "default" : "outline"}
                            onClick={() => toggleStepCompletion(step.id)}
                            className={`${
                              completedSteps.includes(step.id)
                                ? "bg-green-500 hover:bg-green-600"
                                : "bg-transparent border-white/20 hover:bg-white/10"
                            }`}
                          >
                            <CheckCircle className="w-4 h-4" />
                          </Button>
                          <CardTitle className="text-white text-xl">
                            {step.title}
                          </CardTitle>
                        </div>
                        <p className="text-gray-300 mb-4">
                          {step.description}
                        </p>
                        <div className="flex flex-wrap gap-2 mb-4">
                          <Badge className="bg-blue-500/20 text-blue-400 border-blue-500/30">
                            <Clock className="w-3 h-3 mr-1" />
                            {step.timeframe}
                          </Badge>
                          <Badge className="bg-purple-500/20 text-purple-400 border-purple-500/30">
                            <DollarSign className="w-3 h-3 mr-1" />
                            {step.cost}
                          </Badge>
                          <Badge className={getDifficultyColor(step.difficulty)}>
                            {step.difficulty}
                          </Badge>
                        </div>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      {/* Methods */}
                      <div>
                        <h4 className="text-white font-semibold mb-3 flex items-center">
                          <Lightbulb className="w-4 h-4 mr-2 text-yellow-400" />
                          Methods & Tools
                        </h4>
                        <ul className="space-y-2">
                          {step.methods.map((method, idx) => (
                            <li key={idx} className="text-gray-300 text-sm flex items-start">
                              <span className="w-1.5 h-1.5 bg-blue-400 rounded-full mt-2 mr-2 flex-shrink-0"></span>
                              {method}
                            </li>
                          ))}
                        </ul>
                      </div>

                      {/* Tips */}
                      <div>
                        <h4 className="text-white font-semibold mb-3 flex items-center">
                          <Target className="w-4 h-4 mr-2 text-green-400" />
                          Pro Tips
                        </h4>
                        <ul className="space-y-2">
                          {step.tips.map((tip, idx) => (
                            <li key={idx} className="text-gray-300 text-sm flex items-start">
                              <span className="w-1.5 h-1.5 bg-green-400 rounded-full mt-2 mr-2 flex-shrink-0"></span>
                              {tip}
                            </li>
                          ))}
                        </ul>
                      </div>
                    </div>

                    {/* Red Flags */}
                    <div className="mt-6 p-4 bg-red-500/10 border border-red-500/20 rounded-lg">
                      <h4 className="text-white font-semibold mb-3 flex items-center">
                        <AlertCircle className="w-4 h-4 mr-2 text-red-400" />
                        Red Flags to Watch For
                      </h4>
                      <ul className="space-y-2">
                        {step.redFlags.map((flag, idx) => (
                          <li key={idx} className="text-red-300 text-sm flex items-start">
                            <span className="w-1.5 h-1.5 bg-red-400 rounded-full mt-2 mr-2 flex-shrink-0"></span>
                            {flag}
                          </li>
                        ))}
                      </ul>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </motion.div>

            {/* Validation Checklist */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
              className="mb-8"
            >
              <Card className="glass-card border-white/10">
                <CardHeader>
                  <CardTitle className="text-white flex items-center">
                    <CheckCircle className="w-5 h-5 mr-2 text-green-400" />
                    Final Validation Checklist
                  </CardTitle>
                  <p className="text-gray-300 text-sm">
                    Check off each item before proceeding to build your startup
                  </p>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {VALIDATION_CHECKLIST.map((item, index) => (
                      <div 
                        key={index}
                        className="flex items-center space-x-3 p-3 rounded-lg bg-white/5 hover:bg-white/10 transition-colors cursor-pointer"
                        onClick={() => toggleChecklistItem(index)}
                      >
                        <div className={`w-5 h-5 rounded border-2 flex items-center justify-center ${
                          checkedItems.includes(index)
                            ? 'bg-green-500 border-green-500'
                            : 'border-gray-400'
                        }`}>
                          {checkedItems.includes(index) && (
                            <CheckCircle className="w-3 h-3 text-white" />
                          )}
                        </div>
                        <span className={`${
                          checkedItems.includes(index) ? 'text-white' : 'text-gray-300'
                        }`}>
                          {item}
                        </span>
                      </div>
                    ))}
                  </div>
                  
                  {checkedItems.length === VALIDATION_CHECKLIST.length && (
                    <div className="mt-6 p-4 bg-green-500/10 border border-green-500/20 rounded-lg">
                      <div className="flex items-center space-x-2">
                        <CheckCircle className="w-5 h-5 text-green-400" />
                        <span className="text-green-400 font-semibold">
                          Congratulations! Your idea is validated and ready for development.
                        </span>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            </motion.div>

          </div>
        </div>
      </div>
    </>
  );
}
