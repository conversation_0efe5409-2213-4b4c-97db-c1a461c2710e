// <PERSON>ript to create a task for "saas" subreddit
// This will create a scraping task for the saas subreddit

const SUPABASE_URL = 'https://niviihlfsqocuboafudh.supabase.co';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.p1kZQezwzr_7ZHs5Nd8sHZouoY76MmfnHSedeRi7gSc';

async function createSaasTask() {
  try {
    // Get yesterday's date in YYYY-MM-DD format
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    const targetDate = yesterday.toISOString().split('T')[0];
    
    console.log(`Creating task for saas subreddit with target date: ${targetDate}`);
    
    const response = await fetch(`${SUPABASE_URL}/functions/v1/task-creator`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${SUPABASE_ANON_KEY}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        targetDate: targetDate,
        industryIds: [1], // SaaS & Cloud Services industry ID
        forceCreate: false
      }),
    });

    const result = await response.json();
    
    if (response.ok) {
      console.log('✅ Task created successfully!');
      console.log(`Batch ID: ${result.batchId}`);
      console.log(`Tasks created: ${result.tasksCreated}`);
      console.log(`Message: ${result.message}`);
    } else {
      console.error('❌ Failed to create task:');
      console.error(`Status: ${response.status}`);
      console.error(`Message: ${result.message}`);
      if (result.errors) {
        console.error('Errors:', result.errors);
      }
    }
    
    return result;
  } catch (error) {
    console.error('❌ Error creating task:', error.message);
    throw error;
  }
}

// Run the function directly
createSaasTask()
  .then(() => {
    console.log('Script completed');
    process.exit(0);
  })
  .catch((error) => {
    console.error('Script failed:', error);
    process.exit(1);
  });
