// Critical CSS utilities for performance optimization

export const CRITICAL_CSS = `
  /* Critical above-the-fold styles */
  * {
    box-sizing: border-box;
  }
  
  body {
    margin: 0;
    font-family: Inter, system-ui, -apple-system, sans-serif;
    background: linear-gradient(135deg, #1f2937 0%, #000 100%);
    color: white;
    line-height: 1.6;
  }
  
  /* Loading states */
  .loading-spinner {
    width: 2rem;
    height: 2rem;
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }
  
  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
  
  /* Layout containers */
  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
  }
  
  /* Header styles */
  .header {
    background: rgba(31, 41, 55, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    position: sticky;
    top: 0;
    z-index: 50;
  }
  
  /* Navigation */
  .nav-link {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    transition: all 0.2s ease;
  }
  
  .nav-link:hover {
    color: white;
    background: rgba(255, 255, 255, 0.1);
  }
  
  /* Button styles */
  .btn-primary {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
  }
  
  .btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
  }
  
  /* Card styles */
  .card {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 1rem;
    padding: 1.5rem;
    transition: all 0.2s ease;
  }
  
  .card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  }
  
  /* Grid layouts */
  .grid-auto {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
  }
  
  /* Text styles */
  .text-gradient {
    background: linear-gradient(135deg, #60a5fa, #a855f7);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
  
  /* Responsive utilities */
  @media (max-width: 768px) {
    .container {
      padding: 0 0.75rem;
    }
    
    .grid-auto {
      grid-template-columns: 1fr;
      gap: 1rem;
    }
    
    .card {
      padding: 1rem;
    }
  }
  
  /* Performance optimizations */
  .gpu-accelerated {
    transform: translateZ(0);
    will-change: transform;
  }
  
  .stable-layout {
    contain: layout style;
  }
  
  /* Prevent layout shift */
  .min-h-screen {
    min-height: 100vh;
  }
  
  .flex {
    display: flex;
  }
  
  .flex-col {
    flex-direction: column;
  }
  
  .items-center {
    align-items: center;
  }
  
  .justify-center {
    justify-content: center;
  }
  
  .justify-between {
    justify-content: space-between;
  }
  
  /* Hide non-critical content initially */
  .below-fold {
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.3s ease;
  }
  
  .below-fold.visible {
    opacity: 1;
    transform: translateY(0);
  }
`;

// Function to inject critical CSS
export function injectCriticalCSS() {
  if (typeof document === 'undefined') return;
  
  const existingStyle = document.getElementById('critical-css');
  if (existingStyle) return; // Already injected
  
  const style = document.createElement('style');
  style.id = 'critical-css';
  style.textContent = CRITICAL_CSS;
  
  // Insert before any existing stylesheets
  const firstLink = document.querySelector('link[rel="stylesheet"]');
  if (firstLink) {
    document.head.insertBefore(style, firstLink);
  } else {
    document.head.appendChild(style);
  }
}

// Function to load non-critical CSS asynchronously
export function loadNonCriticalCSS() {
  if (typeof document === 'undefined') return;
  
  const links = document.querySelectorAll('link[rel="stylesheet"][media="print"]');
  links.forEach(link => {
    if (link instanceof HTMLLinkElement) {
      link.media = 'all';
    }
  });
}

// Function to observe and animate below-fold content
export function setupBelowFoldAnimation() {
  if (typeof window === 'undefined' || !('IntersectionObserver' in window)) return;
  
  const observer = new IntersectionObserver(
    (entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          entry.target.classList.add('visible');
          observer.unobserve(entry.target);
        }
      });
    },
    {
      rootMargin: '50px 0px',
      threshold: 0.1
    }
  );
  
  // Observe all below-fold elements
  const belowFoldElements = document.querySelectorAll('.below-fold');
  belowFoldElements.forEach(el => observer.observe(el));
  
  return observer;
}

// Function to preload critical resources
export function preloadCriticalResources() {
  if (typeof document === 'undefined') return;

  // Only preload resources that actually exist
  const criticalResources = [
    { href: '/favicon.svg', as: 'image' },
  ];

  // In production, we can preload the built assets
  if (import.meta.env.PROD) {
    // These paths will be correct in production build
    const scripts = document.querySelectorAll('script[src*="/assets/"]');
    const stylesheets = document.querySelectorAll('link[href*="/assets/"][rel="stylesheet"]');

    scripts.forEach(script => {
      if (script instanceof HTMLScriptElement && script.src) {
        criticalResources.push({ href: script.src, as: 'script' });
      }
    });

    stylesheets.forEach(link => {
      if (link instanceof HTMLLinkElement && link.href) {
        criticalResources.push({ href: link.href, as: 'style' });
      }
    });
  }

  criticalResources.forEach(resource => {
    const link = document.createElement('link');
    link.rel = 'preload';
    link.href = resource.href;
    link.as = resource.as;
    if (resource.as === 'font') {
      link.crossOrigin = 'anonymous';
    }
    document.head.appendChild(link);
  });
}

// Initialize all critical CSS optimizations
export function initializeCriticalCSS() {
  injectCriticalCSS();
  preloadCriticalResources();
  
  // Load non-critical CSS after initial render
  requestIdleCallback(() => {
    loadNonCriticalCSS();
    setupBelowFoldAnimation();
  });
}
