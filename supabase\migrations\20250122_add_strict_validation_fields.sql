-- Add enhanced analysis fields to startup_ideas table
ALTER TABLE startup_ideas 
ADD COLUMN IF NOT EXISTS quality_score INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS competitor_analysis TEXT,
ADD COLUMN IF NOT EXISTS feasibility_assessment TEXT,
ADD COLUMN IF NOT EXISTS market_potential_score INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS revenue_model JSONB DEFAULT '[]'::jsonb,
ADD COLUMN IF NOT EXISTS source_post_ids JSONB DEFAULT '[]'::jsonb;

-- Add new strict validation fields
ALTER TABLE startup_ideas 
ADD COLUMN IF NOT EXISTS market_saturation_score INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS pain_severity_score INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS payment_willingness_evidence TEXT,
ADD COLUMN IF NOT EXISTS competitive_moat_strength INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS technical_complexity_score INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS execution_feasibility_score INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS regulatory_risk_score INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS market_timing_score INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS user_validation_count INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS revenue_timeline_months INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS mvp_development_weeks INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS required_team_size INTEGER DEFAULT 1,
ADD COLUMN IF NOT EXISTS overall_viability_score INTEGER DEFAULT 0;

-- Add comments for documentation
COMMENT ON COLUMN startup_ideas.market_saturation_score IS 'Market saturation level (1-10): 1=blue ocean, 10=completely saturated';
COMMENT ON COLUMN startup_ideas.pain_severity_score IS 'Severity of user pain point (1-10): 1=minor annoyance, 10=critical business problem';
COMMENT ON COLUMN startup_ideas.payment_willingness_evidence IS 'Specific evidence of users willing to pay for solution';
COMMENT ON COLUMN startup_ideas.competitive_moat_strength IS 'Strength of competitive advantage (1-10): 1=easily copied, 10=strong defensibility';
COMMENT ON COLUMN startup_ideas.technical_complexity_score IS 'Technical implementation complexity (1-10): 1=simple, 10=requires large engineering team';
COMMENT ON COLUMN startup_ideas.execution_feasibility_score IS 'Execution feasibility (1-10): 1=very difficult, 10=straightforward execution';
COMMENT ON COLUMN startup_ideas.regulatory_risk_score IS 'Regulatory compliance risk (1-10): 1=no compliance issues, 10=heavily regulated';
COMMENT ON COLUMN startup_ideas.market_timing_score IS 'Market timing assessment (1-10): 1=too early/late, 10=perfect timing';
COMMENT ON COLUMN startup_ideas.user_validation_count IS 'Number of users expressing the same pain point';
COMMENT ON COLUMN startup_ideas.revenue_timeline_months IS 'Expected months to first paying customers';
COMMENT ON COLUMN startup_ideas.mvp_development_weeks IS 'Estimated weeks to develop MVP';
COMMENT ON COLUMN startup_ideas.required_team_size IS 'Minimum team size required for execution';
COMMENT ON COLUMN startup_ideas.overall_viability_score IS 'Overall business viability score (1-100)';

-- Add indexes for better query performance on new scoring fields
CREATE INDEX IF NOT EXISTS idx_startup_ideas_market_saturation ON startup_ideas(market_saturation_score);
CREATE INDEX IF NOT EXISTS idx_startup_ideas_pain_severity ON startup_ideas(pain_severity_score);
CREATE INDEX IF NOT EXISTS idx_startup_ideas_overall_viability ON startup_ideas(overall_viability_score);
CREATE INDEX IF NOT EXISTS idx_startup_ideas_execution_feasibility ON startup_ideas(execution_feasibility_score);
