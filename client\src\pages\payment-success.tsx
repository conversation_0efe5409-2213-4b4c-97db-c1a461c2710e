import { useEffect, useState } from 'react';
import { motion } from 'framer-motion';
import { Check<PERSON>ircle, Crown, Heart, Calendar, Filter, ArrowRight, Home } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import ParticleBackground from '@/components/particle-background';
import { useLocation } from 'wouter';

export default function PaymentSuccess() {
  const [, setLocation] = useLocation();
  const [showConfetti, setShowConfetti] = useState(false);

  useEffect(() => {
    setShowConfetti(true);
    // Auto redirect after 10 seconds
    const timer = setTimeout(() => {
      setLocation('/dashboard');
    }, 10000);

    return () => clearTimeout(timer);
  }, [setLocation]);

  const features = [
    {
      icon: Calendar,
      title: 'All Time Periods',
      description: 'Access ideas from any date, not just today'
    },
    {
      icon: Heart,
      title: 'Unlimited Favorites',
      description: 'Save and organize your best startup ideas'
    },
    {
      icon: Filter,
      title: 'Advanced Filtering',
      description: 'Use all filters and sorting options'
    }
  ];

  return (
    <div className="gradient-bg text-white relative overflow-hidden">
      <ParticleBackground />

      <div className="relative z-10 container mx-auto px-4 py-8 flex items-center justify-center py-20">
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.6 }}
          className="max-w-2xl w-full text-center"
        >
          {/* Success Icon */}
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
            className="mb-8"
          >
            <div className="relative inline-block">
              <div className="w-24 h-24 bg-gradient-to-r from-green-400 to-cyan-400 rounded-full flex items-center justify-center mx-auto">
                <CheckCircle className="w-12 h-12 text-white" />
              </div>
              {showConfetti && (
                <motion.div
                  initial={{ scale: 0, rotate: 0 }}
                  animate={{ scale: 1, rotate: 360 }}
                  transition={{ delay: 0.5, duration: 0.8 }}
                  className="absolute -top-2 -right-2"
                >
                  <Crown className="w-8 h-8 text-yellow-400" />
                </motion.div>
              )}
            </div>
          </motion.div>

          {/* Success Message */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
            className="mb-8"
          >
            <h1 className="text-4xl md:text-5xl font-bold mb-4">
              <span className="bg-gradient-to-r from-green-400 to-cyan-400 bg-clip-text text-transparent">
                Payment Successful! 🎉
              </span>
            </h1>
            <p className="text-xl text-gray-300 mb-2">
              Welcome to ScraperDash Pro!
            </p>
            <p className="text-gray-400">
              You now have lifetime access to all premium features
            </p>
          </motion.div>

          {/* Features Unlocked */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.6 }}
            className="mb-8"
          >
            <h2 className="text-2xl font-semibold mb-6 text-cyan-300">
              Features Unlocked
            </h2>
            <div className="grid md:grid-cols-3 gap-4">
              {features.map((feature, index) => {
                const Icon = feature.icon;
                return (
                  <motion.div
                    key={feature.title}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.8 + index * 0.1 }}
                  >
                    <Card className="glass-card border-cyan-500/30 bg-cyan-500/10">
                      <CardContent className="p-4 text-center">
                        <div className="w-12 h-12 bg-gradient-to-r from-cyan-500 to-purple-500 rounded-lg flex items-center justify-center mx-auto mb-3">
                          <Icon className="w-6 h-6 text-white" />
                        </div>
                        <h3 className="font-semibold text-white mb-2">{feature.title}</h3>
                        <p className="text-sm text-gray-300">{feature.description}</p>
                      </CardContent>
                    </Card>
                  </motion.div>
                );
              })}
            </div>
          </motion.div>

          {/* Action Buttons */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 1.2 }}
            className="space-y-4"
          >
            <Button
              onClick={() => setLocation('/dashboard')}
              className="bg-gradient-to-r from-cyan-500 to-purple-500 hover:from-cyan-600 hover:to-purple-600 text-white font-semibold py-3 px-8 text-lg"
            >
              <Home className="w-5 h-5 mr-2" />
              Start Exploring
              <ArrowRight className="w-5 h-5 ml-2" />
            </Button>
            
            <div className="text-sm text-gray-400">
              Redirecting to dashboard in 10 seconds...
            </div>
          </motion.div>

          {/* Thank You Message */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 1.5 }}
            className="mt-8 p-4 glass-card rounded-lg border border-cyan-500/30"
          >
            <p className="text-cyan-300 font-medium">
              Thank you for supporting ScraperDash! 💙
            </p>
            <p className="text-sm text-gray-400 mt-1">
              Your support helps us continue to find and analyze the best startup ideas from Reddit
            </p>
          </motion.div>
        </motion.div>
      </div>
    </div>
  );
}
