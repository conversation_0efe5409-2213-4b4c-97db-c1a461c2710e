import React from 'react';
import { <PERSON> } from 'wouter';
import { Clock, Eye, Calendar, BookOpen } from 'lucide-react';
import { useRelatedBlogPosts } from '@/hooks/use-blog';
import { formatDistanceToNow } from 'date-fns';

interface RelatedArticlesProps {
  currentPostId: number;
  tags: string[];
  category: string;
  className?: string;
}

export default function RelatedArticles({ currentPostId, tags, category, className = '' }: RelatedArticlesProps) {
  const { data: relatedPosts, isLoading } = useRelatedBlogPosts(currentPostId, tags, category, 3);

  if (isLoading) {
    return (
      <div className={`${className}`}>
        <h3 className="text-xl font-bold text-white mb-6 flex items-center">
          <BookOpen className="w-5 h-5 mr-2 text-blue-400" />
          Related Articles
        </h3>
        <div className="space-y-4">
          {[1, 2, 3].map((i) => (
            <div key={i} className="bg-gray-800/50 border border-gray-700 rounded-lg p-4 animate-pulse">
              <div className="h-4 bg-gray-700 rounded mb-2"></div>
              <div className="h-3 bg-gray-700 rounded mb-2 w-3/4"></div>
              <div className="h-3 bg-gray-700 rounded w-1/2"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (!relatedPosts || relatedPosts.length === 0) {
    return null;
  }

  return (
    <div className={`${className}`}>
      <h3 className="text-xl font-bold text-white mb-6 flex items-center">
        <BookOpen className="w-5 h-5 mr-2 text-blue-400" />
        Related Articles
      </h3>
      <div className="space-y-4">
        {relatedPosts.map((post) => (
          <article key={post.id} className="bg-gray-800/50 border border-gray-700 rounded-lg p-4 hover:bg-gray-800 hover:border-gray-600 transition-all duration-200">
            <div className="flex items-start space-x-3">
              <div className="flex-1 min-w-0">
                <h4 className="text-white font-medium text-sm line-clamp-2 mb-2">
                  <Link
                    href={`/blog/${post.slug}`}
                    className="hover:text-blue-400 transition-colors"
                  >
                    {post.title}
                  </Link>
                </h4>
                
                <p className="text-gray-400 text-xs line-clamp-2 mb-3">
                  {post.excerpt}
                </p>
                
                <div className="flex items-center space-x-3 text-xs text-gray-500">
                  <span className="bg-gray-700 text-gray-300 px-2 py-1 rounded">
                    {post.category}
                  </span>
                  <div className="flex items-center">
                    <Clock className="w-3 h-3 mr-1" />
                    {post.read_time} min
                  </div>
                  <div className="flex items-center">
                    <Eye className="w-3 h-3 mr-1" />
                    {post.view_count}
                  </div>
                </div>
                
                <div className="flex items-center text-xs text-gray-500 mt-2">
                  <Calendar className="w-3 h-3 mr-1" />
                  {formatDistanceToNow(new Date(post.published_at), { addSuffix: true })}
                </div>
              </div>
            </div>
          </article>
        ))}
      </div>
    </div>
  );
}
