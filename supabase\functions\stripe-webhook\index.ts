import "jsr:@supabase/functions-js/edge-runtime.d.ts";
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';
import Stripe from 'https://esm.sh/stripe@14.21.0?target=deno';

// This function should NOT verify JWT since it's called by Stripe servers
// We'll verify the webhook signature instead

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

Deno.serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    // Initialize Stripe
    const stripe = new Stripe(Deno.env.get('STRIPE_SECRET_KEY') || '', {
      apiVersion: '2023-10-16',
    });

    // Initialize Supabase client
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    );

    // Get the raw body for signature verification
    const body = await req.text();
    const signature = req.headers.get('stripe-signature');

    console.log('Webhook request received:');
    console.log('- Body length:', body.length);
    console.log('- Signature present:', !!signature);
    console.log('- Signature preview:', signature ? signature.substring(0, 50) + '...' : 'none');

    if (!signature) {
      console.error('No Stripe signature found');
      return new Response('No signature', { status: 400 });
    }

    // Verify webhook signature
    const webhookSecret = Deno.env.get('STRIPE_WEBHOOK_SECRET');
    console.log('- Webhook secret configured:', !!webhookSecret);
    console.log('- Webhook secret preview:', webhookSecret ? webhookSecret.substring(0, 10) + '...' : 'none');

    if (!webhookSecret) {
      console.error('No webhook secret configured');
      return new Response('Webhook secret not configured', { status: 500 });
    }

    let event: Stripe.Event;
    try {
      // Use synchronous method instead of async to avoid Deno.core.runMicrotasks issue
      event = stripe.webhooks.constructEvent(body, signature, webhookSecret);
      console.log('✅ Webhook signature verification successful');
    } catch (err) {
      console.error('❌ Webhook signature verification failed:', err.message);
      console.error('- Error details:', err);
      return new Response(`Webhook Error: ${err.message}`, { status: 400 });
    }

    console.log(`Received webhook event: ${event.type}`);

    // Handle the event
    switch (event.type) {
      case 'payment_intent.succeeded': {
        const paymentIntent = event.data.object as Stripe.PaymentIntent;
        console.log(`Payment succeeded: ${paymentIntent.id}`);

        // Get user ID from metadata
        const userId = paymentIntent.metadata.supabase_user_id;
        if (!userId) {
          console.error('No user ID in payment intent metadata');
          break;
        }

        // Calculate expiration date (1 year from now)
        const expiresAt = new Date();
        expiresAt.setFullYear(expiresAt.getFullYear() + 1);

        // Update user subscription status
        const { error: updateError } = await supabaseClient
          .from('user_profiles')
          .update({
            subscription_status: 'pro',
            subscription_expires_at: expiresAt.toISOString(),
            updated_at: new Date().toISOString(),
          })
          .eq('id', userId);

        if (updateError) {
          console.error('Error updating user subscription:', updateError);
        } else {
          console.log(`Successfully upgraded user ${userId} to pro`);
        }
        break;
      }

      case 'payment_intent.payment_failed': {
        const paymentIntent = event.data.object as Stripe.PaymentIntent;
        console.log(`Payment failed: ${paymentIntent.id}`);
        
        // You could add logic here to handle failed payments
        // For example, send an email notification or update user status
        break;
      }

      default:
        console.log(`Unhandled event type: ${event.type}`);
    }

    return new Response(JSON.stringify({ received: true }), {
      status: 200,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });
  } catch (error) {
    console.error('Webhook error:', error);
    return new Response(
      JSON.stringify({
        error: error.message,
      }),
      {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      }
    );
  }
});
