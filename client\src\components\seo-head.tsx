import { useEffect } from 'react';
import type { StartupIdea } from '@/lib/types';
import { getIdeaShareUrl } from '@/lib/url-utils';

interface SEOHeadProps {
  title?: string;
  description?: string;
  keywords?: string[];
  image?: string;
  url?: string;
  type?: 'website' | 'article';
  idea?: StartupIdea;
  structuredDataType?: 'Article' | 'FAQPage' | 'BreadcrumbList' | 'ItemList' | 'Dataset';
  article?: {
    author: string;
    publishedTime: string;
    modifiedTime?: string;
    section?: string;
    tags?: string[];
  };
  breadcrumbs?: Array<{
    name: string;
    url: string;
  }>;
  itemList?: Array<{
    name: string;
    description: string;
    url: string;
  }>;
  dataset?: {
    name: string;
    description: string;
    creator: string;
    dateCreated: string;
    dateModified?: string;
    keywords: string[];
    distribution?: {
      contentUrl: string;
      encodingFormat: string;
    };
  };
}

export default function SEOHead({
  title,
  description,
  keywords = [],
  image,
  url,
  type = 'website',
  idea,
  structuredDataType,
  article,
  breadcrumbs,
  itemList,
  dataset
}: SEOHeadProps) {
  useEffect(() => {
    // Generate SEO data from idea if provided
    let seoTitle = title;
    let seoDescription = description;
    let seoKeywords = keywords;
    let seoUrl = url;
    let seoImage = image;

    if (idea) {
      seoTitle = `${idea.title} | IdeaHunter`;
      seoDescription = idea.summary.length > 160
        ? `${idea.summary.substring(0, 157)}...`
        : idea.summary;
      seoKeywords = [...idea.keywords, (idea.industry as any)?.name || 'startup'];
      seoUrl = getIdeaShareUrl(idea.id, idea.title);
      // Use favicon.svg as default image since og-image-default.jpg doesn't exist
      const defaultImage = '/favicon.svg';
      const imageUrl = image || defaultImage;
      seoImage = imageUrl.startsWith('http') ? imageUrl : `${window.location.origin}${imageUrl}`;
    }

    // Update document title
    if (seoTitle) {
      document.title = seoTitle;
    }

    // Update meta tags
    updateMetaTag('description', seoDescription || '');
    updateMetaTag('keywords', seoKeywords.join(', '));
    
    // Open Graph tags
    updateMetaProperty('og:title', seoTitle || '');
    updateMetaProperty('og:description', seoDescription || '');
    updateMetaProperty('og:type', type);
    updateMetaProperty('og:url', seoUrl || window.location.href);
    updateMetaProperty('og:site_name', 'IdeaHunter');
    if (seoImage) {
      updateMetaProperty('og:image', seoImage);
      updateMetaProperty('og:image:width', '1200');
      updateMetaProperty('og:image:height', '630');
      updateMetaProperty('og:image:alt', seoTitle || 'IdeaHunter Startup Idea');
    }

    // Twitter Card tags
    updateMetaProperty('twitter:title', seoTitle || '');
    updateMetaProperty('twitter:description', seoDescription || '');
    updateMetaProperty('twitter:card', 'summary_large_image');
    if (seoImage) {
      updateMetaProperty('twitter:image', seoImage);
    }

    // Article-specific meta tags
    if (type === 'article' && article) {
      updateMetaProperty('article:author', article.author);
      updateMetaProperty('article:published_time', article.publishedTime);
      if (article.modifiedTime) {
        updateMetaProperty('article:modified_time', article.modifiedTime);
      }
      if (article.section) {
        updateMetaProperty('article:section', article.section);
      }
      if (article.tags) {
        article.tags.forEach(tag => {
          const meta = document.createElement('meta');
          meta.setAttribute('property', 'article:tag');
          meta.content = tag;
          document.head.appendChild(meta);
        });
      }
    }

    // Add structured data
    if (idea) {
      updateStructuredData(idea);
    } else if (structuredDataType) {
      updateCustomStructuredData(structuredDataType, {
        title: seoTitle,
        description: seoDescription,
        url: seoUrl,
        article,
        breadcrumbs,
        itemList,
        dataset
      });
    }

    // Cleanup function to restore default meta tags when component unmounts
    return () => {
      // Restore default title
      document.title = 'IdeaHunter - AI-Powered Reddit Trend Discovery';
      
      // Restore default meta tags
      updateMetaTag('description', 'Discover trending startup opportunities from Reddit communities using AI-powered analysis.');
      updateMetaTag('keywords', 'startup ideas, reddit scraper, trend analysis, entrepreneurship, AI analysis');
      
      updateMetaProperty('og:title', 'IdeaHunter - AI-Powered Reddit Trend Discovery');
      updateMetaProperty('og:description', 'Discover trending startup opportunities from Reddit communities using AI-powered analysis.');
      updateMetaProperty('og:type', 'website');
      
      updateMetaProperty('twitter:title', 'IdeaHunter - AI-Powered Reddit Trend Discovery');
      updateMetaProperty('twitter:description', 'Discover trending startup opportunities from Reddit communities using AI-powered analysis.');
      
      // Remove structured data
      removeStructuredData();
    };
  }, [title, description, keywords, image, url, type, idea]);

  return null; // This component doesn't render anything
}

function updateMetaTag(name: string, content: string) {
  let meta = document.querySelector(`meta[name="${name}"]`) as HTMLMetaElement;
  if (!meta) {
    meta = document.createElement('meta');
    meta.name = name;
    document.head.appendChild(meta);
  }
  meta.content = content;
}

function updateMetaProperty(property: string, content: string) {
  let meta = document.querySelector(`meta[property="${property}"]`) as HTMLMetaElement;
  if (!meta) {
    meta = document.createElement('meta');
    meta.setAttribute('property', property);
    document.head.appendChild(meta);
  }
  meta.content = content;
}

function updateStructuredData(idea: StartupIdea) {
  // Remove existing structured data
  removeStructuredData();

  // Create new structured data
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "Article",
    "headline": idea.title,
    "description": idea.summary,
    "author": {
      "@type": "Organization",
      "name": "IdeaHunter"
    },
    "publisher": {
      "@type": "Organization",
      "name": "IdeaHunter"
    },
    "datePublished": idea.createdAt,
    "dateModified": idea.updatedAt,
    "keywords": idea.keywords.join(', '),
    "about": {
      "@type": "Thing",
      "name": (idea.industry as any)?.name || 'Startup Idea'
    },
    "url": getIdeaShareUrl(idea.id, idea.title)
  };

  const script = document.createElement('script');
  script.type = 'application/ld+json';
  script.id = 'structured-data-idea';
  script.textContent = JSON.stringify(structuredData);
  document.head.appendChild(script);
}

function removeStructuredData() {
  const existingScripts = document.querySelectorAll('script[type="application/ld+json"]');
  existingScripts.forEach(script => script.remove());
}

function updateCustomStructuredData(
  type: 'Article' | 'FAQPage' | 'BreadcrumbList' | 'ItemList' | 'Dataset',
  data: {
    title?: string;
    description?: string;
    url?: string;
    article?: {
      author: string;
      publishedTime: string;
      modifiedTime?: string;
      section?: string;
      tags?: string[];
    };
    breadcrumbs?: Array<{
      name: string;
      url: string;
    }>;
    itemList?: Array<{
      name: string;
      description: string;
      url: string;
    }>;
    dataset?: {
      name: string;
      description: string;
      creator: string;
      dateCreated: string;
      dateModified?: string;
      keywords: string[];
      distribution?: {
        contentUrl: string;
        encodingFormat: string;
      };
    };
  }
) {
  // Remove existing structured data
  removeStructuredData();

  let structuredData: any = {
    "@context": "https://schema.org"
  };

  switch (type) {
    case 'Article':
      structuredData = {
        ...structuredData,
        "@type": "Article",
        "headline": data.title,
        "description": data.description,
        "author": {
          "@type": "Organization",
          "name": data.article?.author || "IdeaHunter Team"
        },
        "publisher": {
          "@type": "Organization",
          "name": "IdeaHunter",
          "logo": {
            "@type": "ImageObject",
            "url": `${window.location.origin}/favicon.svg`
          }
        },
        "datePublished": data.article?.publishedTime,
        "dateModified": data.article?.modifiedTime || data.article?.publishedTime,
        "mainEntityOfPage": {
          "@type": "WebPage",
          "@id": data.url
        },
        "url": data.url
      };

      if (data.article?.tags) {
        structuredData.keywords = data.article.tags.join(', ');
      }
      break;

    case 'BreadcrumbList':
      if (data.breadcrumbs) {
        structuredData = {
          ...structuredData,
          "@type": "BreadcrumbList",
          "itemListElement": data.breadcrumbs.map((crumb, index) => ({
            "@type": "ListItem",
            "position": index + 1,
            "name": crumb.name,
            "item": crumb.url
          }))
        };
      }
      break;

    case 'FAQPage':
      // This would be used for FAQ-style content
      structuredData = {
        ...structuredData,
        "@type": "FAQPage",
        "mainEntity": []
      };
      break;

    case 'ItemList':
      if (data.itemList) {
        structuredData = {
          ...structuredData,
          "@type": "ItemList",
          "numberOfItems": data.itemList.length,
          "itemListElement": data.itemList.map((item, index) => ({
            "@type": "ListItem",
            "position": index + 1,
            "item": {
              "@type": "CreativeWork",
              "name": item.name,
              "description": item.description,
              "url": item.url
            }
          }))
        };
      }
      break;

    case 'Dataset':
      if (data.dataset) {
        structuredData = {
          ...structuredData,
          "@type": "Dataset",
          "name": data.dataset.name,
          "description": data.dataset.description,
          "creator": {
            "@type": "Organization",
            "name": data.dataset.creator
          },
          "dateCreated": data.dataset.dateCreated,
          "dateModified": data.dataset.dateModified || data.dataset.dateCreated,
          "keywords": data.dataset.keywords,
          "license": "https://creativecommons.org/licenses/by/4.0/",
          "isAccessibleForFree": true
        };

        if (data.dataset.distribution) {
          structuredData.distribution = {
            "@type": "DataDownload",
            "contentUrl": data.dataset.distribution.contentUrl,
            "encodingFormat": data.dataset.distribution.encodingFormat
          };
        }
      }
      break;
  }

  // Add structured data to page
  const script = document.createElement('script');
  script.type = 'application/ld+json';
  script.textContent = JSON.stringify(structuredData);
  document.head.appendChild(script);
}
