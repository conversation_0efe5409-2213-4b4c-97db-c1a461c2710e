/**
 * URL utilities for generating SEO-friendly URLs
 */

/**
 * Convert a string to a URL-friendly slug
 */
export function createSlug(text: string): string {
  return text
    .toLowerCase()
    .trim()
    // Replace spaces and special characters with hyphens
    .replace(/[^\w\s-]/g, '')
    .replace(/[\s_-]+/g, '-')
    // Remove leading/trailing hyphens
    .replace(/^-+|-+$/g, '')
    // Limit length to 60 characters for SEO
    .substring(0, 60)
    .replace(/-+$/, ''); // Remove trailing hyphen if substring cuts in middle of word
}

/**
 * Generate idea URL path
 */
export function getIdeaUrl(id: number, title: string): string {
  const slug = createSlug(title);
  return `/idea/${id}/${slug}`;
}

/**
 * Generate idea share URL (absolute)
 */
export function getIdeaShareUrl(id: number, title: string, baseUrl?: string): string {
  const path = getIdeaUrl(id, title);
  const base = baseUrl || window.location.origin;
  return `${base}${path}`;
}

/**
 * Extract idea ID from URL params
 */
export function extractIdeaId(idParam: string | undefined): number | undefined {
  if (!idParam) return undefined;
  const id = parseInt(idParam, 10);
  return isNaN(id) ? undefined : id;
}

/**
 * Validate and sanitize slug
 */
export function validateSlug(slug: string): boolean {
  // Check if slug matches our expected format
  return /^[a-z0-9-]+$/.test(slug) && slug.length <= 60;
}
