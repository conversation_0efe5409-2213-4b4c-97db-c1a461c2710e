import { motion } from "framer-motion";
import { useMemo } from "react";

export default function ParticleBackground() {
  // Pre-calculate particle positions to prevent layout shift
  const particles = useMemo(() => {
    const cyanParticles = Array.from({ length: 20 }, (_, i) => ({
      id: i,
      left: (i * 5.26) % 100, // Deterministic positioning
      top: (i * 7.89) % 100,
      delay: (i * 0.1) % 2,
      duration: 3 + (i % 3),
    }));

    const purpleParticles = Array.from({ length: 15 }, (_, i) => ({
      id: i,
      left: (i * 6.67) % 100,
      top: (i * 8.33) % 100,
      delay: (i * 0.2) % 3,
      duration: 4 + (i % 2),
    }));

    return { cyanParticles, purpleParticles };
  }, []);

  return (
    <div className="particle-bg absolute inset-0 z-0 stable-layout">
      <motion.div
        className="absolute inset-0 gpu-accelerated"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 2 }}
      >
        {/* Cyan particles with fixed positions */}
        {particles.cyanParticles.map((particle) => (
          <motion.div
            key={`cyan-${particle.id}`}
            className="absolute w-2 h-2 bg-cyan-400/20 rounded-full"
            style={{
              left: `${particle.left}%`,
              top: `${particle.top}%`,
              transform: 'translateZ(0)', // Force GPU acceleration
            }}
            animate={{
              y: [0, -20, 0],
              opacity: [0.2, 0.8, 0.2],
            }}
            transition={{
              duration: particle.duration,
              repeat: Infinity,
              delay: particle.delay,
              ease: "easeInOut",
            }}
          />
        ))}

        {/* Purple particles with fixed positions */}
        {particles.purpleParticles.map((particle) => (
          <motion.div
            key={`purple-${particle.id}`}
            className="absolute w-1 h-1 bg-purple-400/30 rounded-full"
            style={{
              left: `${particle.left}%`,
              top: `${particle.top}%`,
              transform: 'translateZ(0)', // Force GPU acceleration
            }}
            animate={{
              x: [-10, 10, -10],
              y: [0, -15, 0],
              opacity: [0.1, 0.6, 0.1],
            }}
            transition={{
              duration: particle.duration,
              repeat: Infinity,
              delay: particle.delay,
              ease: "easeInOut",
            }}
          />
        ))}
      </motion.div>
    </div>
  );
}
