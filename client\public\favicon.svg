<?xml version="1.0" encoding="UTF-8"?>
<svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background with gradient from cyan to purple -->
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#22d3ee;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#a855f7;stop-opacity:1" />
    </linearGradient>
  </defs>

  <!-- Rounded rectangle background -->
  <rect x="0" y="0" width="32" height="32" rx="8" ry="8" fill="url(#bgGradient)"/>

  <!-- Rocket icon (simplified version matching lucide-react Rocket) -->
  <g fill="white">
    <!-- Rocket body -->
    <path d="M16 6L19 9L19 19L16 22L13 19L13 9L16 6Z"/>
    <!-- Rocket fins -->
    <path d="M13 15L10 17L10 19L13 17Z"/>
    <path d="M19 15L22 17L22 19L19 17Z"/>
    <!-- Rocket window -->
    <circle cx="16" cy="12" r="1.5" fill="url(#bgGradient)"/>
    <!-- Rocket flame -->
    <path d="M14.5 22L16 25L17.5 22Z" fill="#ff6b6b"/>
  </g>
</svg>