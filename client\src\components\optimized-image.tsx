import { useState, useRef, useEffect } from 'react';
import { cn } from '@/lib/utils';

interface OptimizedImageProps {
  src: string;
  alt: string;
  className?: string;
  width?: number;
  height?: number;
  priority?: boolean; // For above-the-fold images
  placeholder?: string; // Placeholder image URL
  onLoad?: () => void;
  onError?: () => void;
}

export default function OptimizedImage({
  src,
  alt,
  className = '',
  width,
  height,
  priority = false,
  placeholder = '/placeholder-image.jpg',
  onLoad,
  onError
}: OptimizedImageProps) {
  const [isLoaded, setIsLoaded] = useState(false);
  const [hasError, setHasError] = useState(false);
  const [isInView, setIsInView] = useState(priority); // Load immediately if priority
  const imgRef = useRef<HTMLImageElement>(null);

  // Intersection Observer for lazy loading
  useEffect(() => {
    if (priority || isInView) return;

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsInView(true);
          observer.disconnect();
        }
      },
      {
        rootMargin: '50px' // Start loading 50px before the image comes into view
      }
    );

    if (imgRef.current) {
      observer.observe(imgRef.current);
    }

    return () => observer.disconnect();
  }, [priority, isInView]);

  const handleLoad = () => {
    setIsLoaded(true);
    onLoad?.();
  };

  const handleError = () => {
    setHasError(true);
    onError?.();
  };

  // Generate responsive srcSet for better performance
  const generateSrcSet = (baseSrc: string) => {
    if (!baseSrc || baseSrc.startsWith('data:') || baseSrc.includes('placeholder')) {
      return undefined;
    }

    // For external images, we can't generate srcSet
    if (baseSrc.startsWith('http')) {
      return undefined;
    }

    // For local images, generate different sizes
    const sizes = [320, 640, 768, 1024, 1280];
    return sizes
      .map(size => `${baseSrc}?w=${size} ${size}w`)
      .join(', ');
  };

  const srcSet = generateSrcSet(src);

  return (
    <div 
      ref={imgRef}
      className={cn('relative overflow-hidden', className)}
      style={{ width, height }}
    >
      {/* Placeholder/Loading state */}
      {!isLoaded && !hasError && (
        <div 
          className="absolute inset-0 bg-gray-800 animate-pulse flex items-center justify-center"
          aria-hidden="true"
        >
          <div className="w-8 h-8 border-2 border-gray-600 border-t-gray-400 rounded-full animate-spin" />
        </div>
      )}

      {/* Error state */}
      {hasError && (
        <div 
          className="absolute inset-0 bg-gray-800 flex items-center justify-center text-gray-400"
          aria-hidden="true"
        >
          <div className="text-center">
            <div className="w-12 h-12 mx-auto mb-2 opacity-50">
              <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M21 19V5c0-1.1-.9-2-2-2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2zM8.5 13.5l2.5 3.01L14.5 12l4.5 6H5l3.5-4.5z"/>
              </svg>
            </div>
            <p className="text-xs">Image not available</p>
          </div>
        </div>
      )}

      {/* Actual image */}
      {(isInView || priority) && (
        <img
          src={hasError ? placeholder : src}
          srcSet={!hasError ? srcSet : undefined}
          sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
          alt={alt}
          width={width}
          height={height}
          loading={priority ? 'eager' : 'lazy'}
          decoding="async"
          onLoad={handleLoad}
          onError={handleError}
          className={cn(
            'transition-opacity duration-300',
            isLoaded ? 'opacity-100' : 'opacity-0',
            'w-full h-full object-cover'
          )}
        />
      )}
    </div>
  );
}

// Helper component for avatar images
export function OptimizedAvatar({
  src,
  alt,
  size = 40,
  className = '',
  fallbackText = '?'
}: {
  src?: string;
  alt: string;
  size?: number;
  className?: string;
  fallbackText?: string;
}) {
  const [hasError, setHasError] = useState(!src);

  if (!src || hasError) {
    return (
      <div 
        className={cn(
          'flex items-center justify-center bg-gradient-to-br from-cyan-500 to-blue-600 text-white font-semibold rounded-full',
          className
        )}
        style={{ width: size, height: size }}
        aria-label={alt}
      >
        {fallbackText.charAt(0).toUpperCase()}
      </div>
    );
  }

  return (
    <OptimizedImage
      src={src}
      alt={alt}
      width={size}
      height={size}
      className={cn('rounded-full', className)}
      priority={true} // Avatars are usually above the fold
      onError={() => setHasError(true)}
    />
  );
}

// Helper component for idea/startup logos
export function StartupLogo({
  idea,
  size = 48,
  className = ''
}: {
  idea: { title: string; id: number };
  size?: number;
  className?: string;
}) {
  // Generate a consistent color based on the idea ID
  const colors = [
    'from-red-500 to-pink-500',
    'from-blue-500 to-cyan-500', 
    'from-green-500 to-emerald-500',
    'from-purple-500 to-violet-500',
    'from-orange-500 to-yellow-500',
    'from-indigo-500 to-blue-500'
  ];
  
  const colorIndex = idea.id % colors.length;
  const gradientClass = colors[colorIndex];
  
  // Get first letter of the title
  const firstLetter = idea.title.charAt(0).toUpperCase();
  
  return (
    <div 
      className={cn(
        `flex items-center justify-center bg-gradient-to-br ${gradientClass} text-white font-bold rounded-lg shadow-lg`,
        className
      )}
      style={{ width: size, height: size, fontSize: size * 0.4 }}
      aria-label={`${idea.title} logo`}
    >
      {firstLetter}
    </div>
  );
}
