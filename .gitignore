# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnp
.pnp.js

# Production builds
/dist
/build
/out

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Operating System
.DS_Store
Thumbs.db

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# Logs
logs
*.log

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# next.js build output
.next

# nuxt.js build output
.nuxt

# vuepress build output
.vuepress/dist

# Serverless directories
.serverless

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# Temporary files
tmp/
temp/

# Database
*.sqlite
*.db

# Supabase
.supabase/
supabase/.temp/

# Replit specific
.replit
replit.nix

# Development files
debug-env.js
test-*.ts
*.test.js
*.spec.js

# Backup files
*.bak
*.backup

# OS generated files
ehthumbs.db
Desktop.ini