import { useEffect, useState } from 'react';

interface MobileOptimizationReport {
  isResponsive: boolean;
  viewportMeta: boolean;
  touchTargets: boolean;
  textReadability: boolean;
  imageOptimization: boolean;
  loadTime: number;
  issues: string[];
  score: number;
}

// Mobile optimization checker
export function useMobileOptimization(): MobileOptimizationReport {
  const [report, setReport] = useState<MobileOptimizationReport>({
    isResponsive: false,
    viewportMeta: false,
    touchTargets: false,
    textReadability: false,
    imageOptimization: false,
    loadTime: 0,
    issues: [],
    score: 0
  });

  useEffect(() => {
    const checkMobileOptimization = () => {
      const issues: string[] = [];
      let score = 0;

      // Check viewport meta tag
      const viewportMeta = document.querySelector('meta[name="viewport"]');
      const hasViewportMeta = !!viewportMeta;
      if (hasViewportMeta) score += 20;
      else issues.push('Missing viewport meta tag');

      // Check responsive design
      const isResponsive = window.innerWidth <= 768 ? 
        document.body.scrollWidth <= window.innerWidth : true;
      if (isResponsive) score += 25;
      else issues.push('Content overflows on mobile devices');

      // Check touch target sizes
      const buttons = document.querySelectorAll('button, a, input[type="button"], input[type="submit"]');
      let touchTargetsOk = true;
      buttons.forEach(button => {
        const rect = button.getBoundingClientRect();
        if (rect.width < 44 || rect.height < 44) {
          touchTargetsOk = false;
        }
      });
      if (touchTargetsOk) score += 20;
      else issues.push('Some touch targets are too small (minimum 44px)');

      // Check text readability
      const textElements = document.querySelectorAll('p, span, div, h1, h2, h3, h4, h5, h6');
      let textReadable = true;
      textElements.forEach(element => {
        const styles = window.getComputedStyle(element);
        const fontSize = parseInt(styles.fontSize);
        if (fontSize < 16) {
          textReadable = false;
        }
      });
      if (textReadable) score += 15;
      else issues.push('Some text is too small for mobile reading (minimum 16px)');

      // Check image optimization
      const images = document.querySelectorAll('img');
      let imagesOptimized = true;
      images.forEach(img => {
        if (!img.getAttribute('alt')) {
          imagesOptimized = false;
        }
        if (!img.getAttribute('loading') && !img.getAttribute('data-src')) {
          // Not lazy loaded
        }
      });
      if (imagesOptimized) score += 20;
      else issues.push('Some images missing alt tags or not optimized');

      // Measure load time (simplified)
      const loadTime = performance.timing.loadEventEnd - performance.timing.navigationStart;

      setReport({
        isResponsive,
        viewportMeta: hasViewportMeta,
        touchTargets: touchTargetsOk,
        textReadability: textReadable,
        imageOptimization: imagesOptimized,
        loadTime,
        issues,
        score
      });
    };

    // Run check after page load
    if (document.readyState === 'complete') {
      checkMobileOptimization();
    } else {
      window.addEventListener('load', checkMobileOptimization);
      return () => window.removeEventListener('load', checkMobileOptimization);
    }
  }, []);

  return report;
}

// Mobile-first CSS utilities
export const mobileStyles = {
  // Touch-friendly button sizes
  touchButton: 'min-h-[44px] min-w-[44px] p-3',
  
  // Readable text sizes
  bodyText: 'text-base leading-relaxed', // 16px base
  smallText: 'text-sm', // 14px minimum
  
  // Mobile-optimized spacing
  mobileSpacing: 'px-4 py-6 md:px-6 md:py-8',
  
  // Responsive containers
  mobileContainer: 'max-w-full px-4 sm:px-6 lg:px-8',
  
  // Mobile navigation
  mobileNav: 'fixed bottom-0 left-0 right-0 bg-gray-900 border-t border-gray-700 md:relative md:bottom-auto md:border-t-0',
  
  // Mobile-friendly forms
  mobileForm: 'space-y-4',
  mobileInput: 'w-full min-h-[44px] px-4 py-3 text-base',
  
  // Mobile cards
  mobileCard: 'p-4 rounded-lg shadow-sm',
  
  // Mobile typography
  mobileHeading: 'text-xl sm:text-2xl md:text-3xl font-bold leading-tight',
  
  // Mobile images
  mobileImage: 'w-full h-auto max-w-full',
  
  // Mobile tables (responsive)
  mobileTable: 'block md:table overflow-x-auto whitespace-nowrap md:whitespace-normal'
};

// Mobile optimization component
export default function MobileOptimizationChecker() {
  const report = useMobileOptimization();

  // Only show in development
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  return (
    <div className="fixed bottom-4 right-4 bg-gray-800 text-white p-4 rounded-lg shadow-lg max-w-sm z-50">
      <h3 className="font-bold mb-2">Mobile Optimization</h3>
      <div className="text-sm space-y-1">
        <div>Score: {report.score}/100</div>
        <div className={report.viewportMeta ? 'text-green-400' : 'text-red-400'}>
          Viewport Meta: {report.viewportMeta ? '✓' : '✗'}
        </div>
        <div className={report.isResponsive ? 'text-green-400' : 'text-red-400'}>
          Responsive: {report.isResponsive ? '✓' : '✗'}
        </div>
        <div className={report.touchTargets ? 'text-green-400' : 'text-red-400'}>
          Touch Targets: {report.touchTargets ? '✓' : '✗'}
        </div>
        <div className={report.textReadability ? 'text-green-400' : 'text-red-400'}>
          Text Readable: {report.textReadability ? '✓' : '✗'}
        </div>
        {report.issues.length > 0 && (
          <details className="mt-2">
            <summary className="cursor-pointer text-yellow-400">Issues ({report.issues.length})</summary>
            <ul className="mt-1 text-xs space-y-1">
              {report.issues.map((issue, index) => (
                <li key={index} className="text-red-300">• {issue}</li>
              ))}
            </ul>
          </details>
        )}
      </div>
    </div>
  );
}

// Hook for mobile detection
export function useIsMobile() {
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 768);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  return isMobile;
}

// Mobile-specific performance optimizations
export function optimizeForMobile() {
  // Reduce animations on mobile for better performance
  if (window.innerWidth <= 768) {
    document.documentElement.style.setProperty('--animation-duration', '0.2s');
  }

  // Optimize touch events
  document.addEventListener('touchstart', function() {}, { passive: true });
  document.addEventListener('touchmove', function() {}, { passive: true });

  // Prevent zoom on input focus (iOS)
  const inputs = document.querySelectorAll('input, select, textarea');
  inputs.forEach(input => {
    if (input instanceof HTMLElement) {
      input.style.fontSize = '16px';
    }
  });
}

// Mobile-friendly error boundaries
export function MobileErrorBoundary({ children }: { children: React.ReactNode }) {
  const [hasError, setHasError] = useState(false);
  const isMobile = useIsMobile();

  if (hasError) {
    return (
      <div className={`p-4 text-center ${isMobile ? 'min-h-screen flex flex-col justify-center' : ''}`}>
        <h2 className="text-xl font-bold mb-4">Something went wrong</h2>
        <p className="text-gray-600 mb-4">
          {isMobile 
            ? "We're having trouble loading this page on your device." 
            : "An error occurred while loading this page."
          }
        </p>
        <button 
          onClick={() => window.location.reload()}
          className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700"
        >
          Reload Page
        </button>
      </div>
    );
  }

  return <>{children}</>;
}
