import React, { useState, useEffect } from 'react';
import { Search, X, Clock, Eye } from 'lucide-react';
import { Link } from 'wouter';
import { useSearchBlogPosts } from '@/hooks/use-blog';
import { formatDistanceToNow } from 'date-fns';
import { useDebounce } from '@/hooks/use-debounce';

interface BlogSearchProps {
  onClose?: () => void;
  className?: string;
}

export default function BlogSearch({ onClose, className = '' }: BlogSearchProps) {
  const [query, setQuery] = useState('');
  const [isOpen, setIsOpen] = useState(false);
  const debouncedQuery = useDebounce(query, 300);
  const { data: searchResults, isLoading } = useSearchBlogPosts(debouncedQuery);

  useEffect(() => {
    setIsOpen(!!debouncedQuery.trim());
  }, [debouncedQuery]);

  const handleClose = () => {
    setQuery('');
    setIsOpen(false);
    onClose?.();
  };

  return (
    <div className={`relative ${className}`}>
      {/* Search Input */}
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
        <input
          type="text"
          placeholder="Search articles..."
          value={query}
          onChange={(e) => setQuery(e.target.value)}
          className="w-full pl-10 pr-10 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        />
        {query && (
          <button
            onClick={handleClose}
            className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white"
          >
            <X className="w-5 h-5" />
          </button>
        )}
      </div>

      {/* Search Results Dropdown */}
      {isOpen && (
        <div className="absolute top-full left-0 right-0 mt-2 bg-gray-800 border border-gray-700 rounded-lg shadow-xl z-50 max-h-96 overflow-y-auto">
          {isLoading ? (
            <div className="p-4 text-center text-gray-400">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500 mx-auto mb-2"></div>
              Searching...
            </div>
          ) : searchResults && searchResults.length > 0 ? (
            <div className="py-2">
              <div className="px-4 py-2 text-sm text-gray-400 border-b border-gray-700">
                Found {searchResults.length} article{searchResults.length !== 1 ? 's' : ''}
              </div>
              {searchResults.map((post) => (
                <Link
                  key={post.slug}
                  href={`/blog/${post.slug}`}
                  className="block px-4 py-3 hover:bg-gray-700 transition-colors"
                  onClick={handleClose}
                >
                  <div className="flex items-start space-x-3">
                    <div className="flex-1 min-w-0">
                      <h3 className="text-white font-medium text-sm line-clamp-2 mb-1">
                        {post.title}
                      </h3>
                      <p className="text-gray-400 text-xs line-clamp-2 mb-2">
                        {post.excerpt}
                      </p>
                      <div className="flex items-center space-x-4 text-xs text-gray-500">
                        <span className="bg-blue-900 text-blue-300 px-2 py-1 rounded">
                          {post.category}
                        </span>
                        <div className="flex items-center">
                          <Clock className="w-3 h-3 mr-1" />
                          {post.read_time} min read
                        </div>
                        <div className="flex items-center">
                          <Eye className="w-3 h-3 mr-1" />
                          {post.view_count} views
                        </div>
                        <span>
                          {formatDistanceToNow(new Date(post.published_at), { addSuffix: true })}
                        </span>
                      </div>
                    </div>
                  </div>
                </Link>
              ))}
            </div>
          ) : debouncedQuery.trim() ? (
            <div className="p-4 text-center text-gray-400">
              <Search className="w-8 h-8 mx-auto mb-2 opacity-50" />
              <p>No articles found for "{debouncedQuery}"</p>
              <p className="text-sm mt-1">Try different keywords or browse by category</p>
            </div>
          ) : null}
        </div>
      )}

      {/* Backdrop */}
      {isOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-40"
          onClick={handleClose}
        />
      )}
    </div>
  );
}
