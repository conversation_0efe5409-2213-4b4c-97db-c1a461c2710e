import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/lib/queryClient';
import { useAuth } from '@/hooks/use-auth.tsx';
import { useFeatureAccess } from '@/hooks/use-subscription';
import type { StartupIdea, DailyStats, IdeasResponse } from '@/lib/types';
import { useMemo } from 'react';
import { getPacificTimeDate, getPacificTimeYesterday, getPacificTimeDateRange } from '@/lib/utils';

interface UseIdeasFilters {
  industryId?: number;
  keywords?: string;
  minUpvotes?: number;
  sortBy?: 'upvotes' | 'comments' | 'recent' | 'confidence';
  timeRange?: 'today' | 'yesterday' | 'week' | 'month' | 'all';
  page?: number;
  pageSize?: number;
}

export function useIdeas(filters: UseIdeasFilters = {}) {
  const { page = 1, pageSize = 20 } = filters;
  const { user } = useAuth();
  const { canAccessAllTimeRanges } = useFeatureAccess();

  // 添加时间戳和随机数确保每次查询都是唯一的
  const uniqueKey = useMemo(() => Math.random().toString(36), [
    filters.industryId, 
    filters.keywords, 
    filters.sortBy, 
    filters.minUpvotes, 
    filters.timeRange
  ]);

  return useQuery<IdeasResponse>({
    queryKey: ['ideas', JSON.stringify(filters), user?.id, uniqueKey],
    queryFn: async ({ signal }) => {
      // For non-authenticated users, restrict to today's top 10 ideas only
      const isAuthenticated = !!user;
      
      let query = supabase
        .from('startup_ideas')
        .select(`
          *,
          industry:industries!industry_id(*)
        `, { count: 'exact' });

      // For unauthenticated users, only show today's top 10 ideas (Pacific time)
      if (!isAuthenticated) {
        const todayPacific = getPacificTimeDate();

        query = query
          .eq('target_date', todayPacific)
          .order('upvotes', { ascending: false })
          .limit(10);
      } else {
        // For authenticated users, determine effective time range
        // Free users can access today and yesterday, pro users can access all
        let effectiveTimeRange = filters.timeRange;
        if (!canAccessAllTimeRanges) {
          // Free users: restrict to today and yesterday only
          if (filters.timeRange && !['today', 'yesterday'].includes(filters.timeRange)) {
            effectiveTimeRange = 'today';
          }
        }
        // Apply filters first for authenticated users
        if (filters.industryId) {
          query = query.eq('industry_id', filters.industryId);
        }

        if (filters.keywords) {
          query = query.or(`title.ilike.%${filters.keywords}%,summary.ilike.%${filters.keywords}%`);
        }

        if (filters.minUpvotes) {
          query = query.gte('upvotes', filters.minUpvotes);
        }

        // Apply time range filter based on target_date using Pacific time
        if (effectiveTimeRange && effectiveTimeRange !== 'all') {
          switch (effectiveTimeRange) {
            case 'today':
              // Get ideas based on today's target date (Pacific time)
              const todayPacific = getPacificTimeDate();
              query = query.eq('target_date', todayPacific);
              break;
            case 'yesterday':
              // Get ideas based on yesterday's target date (Pacific time)
              const yesterdayPacific = getPacificTimeYesterday();
              query = query.eq('target_date', yesterdayPacific);
              break;
            case 'week':
              // Get ideas from the last 7 days of target dates (Pacific time)
              const weekStartPacific = getPacificTimeDateRange(7);
              query = query.gte('target_date', weekStartPacific);
              break;
            case 'month':
              // Get ideas from the last 30 days of target dates (Pacific time)
              const monthStartPacific = getPacificTimeDateRange(30);
              query = query.gte('target_date', monthStartPacific);
              break;
            default:
              // For 'all' or any other case, don't apply date filter
              break;
          }
        }

        // Apply sorting after filters
        switch (filters.sortBy) {
          case 'upvotes':
            query = query.order('upvotes', { ascending: false });
            break;
          case 'comments':
            query = query.order('comments', { ascending: false });
            break;
          case 'confidence':
            query = query.order('confidence_score', { ascending: false });
            break;
          case 'recent':
          default:
            // 使用target_date排序，显示最新目标日期的ideas在前面
            query = query.order('target_date', { ascending: false });
            break;
        }

        // Apply pagination last, after all filters are applied
        query = query.range((page - 1) * pageSize, page * pageSize - 1);
      }

      // Use AbortController for request cancellation to prevent race conditions
      const { data: ideas, error, count } = await query.abortSignal(signal);

      if (error) {
        // Handle range not satisfiable error gracefully
        if (error.message.includes('Range Not Satisfiable') || error.code === 'PGRST103') {
          console.warn('Range not satisfiable, returning empty result');
          return {
            ideas: [],
            total: 0,
            page: 1,
            pageSize,
            totalPages: 0,
            isLimited: !isAuthenticated
          };
        }
        throw new Error(`Failed to fetch ideas: ${error.message}`);
      }

      // Map database field names to frontend expected field names
      const mappedIdeas = (ideas || []).map(idea => ({
        id: idea.id,
        title: idea.title,
        summary: idea.summary,
        industryId: idea.industry_id,
        upvotes: idea.upvotes,
        comments: idea.comments,
        keywords: idea.keywords || [],
        subreddit: idea.subreddit,
        redditPostUrls: idea.reddit_post_urls || [],
        existingSolutions: idea.existing_solutions,
        solutionGaps: idea.solution_gaps,
        marketSize: idea.market_size,
        targetDate: idea.target_date,
        createdAt: idea.created_at,
        updatedAt: idea.updated_at,
        confidenceScore: idea.confidence_score,
        industry: idea.industry
      }));

      // For unauthenticated users, return limited data
      const totalCount = !isAuthenticated ? 10 : (count || 0);
      const currentPageSize = !isAuthenticated ? 10 : pageSize;

      return {
        ideas: mappedIdeas,
        total: totalCount,
        page,
        pageSize: currentPageSize,
        totalPages: Math.ceil(totalCount / currentPageSize),
        isLimited: !isAuthenticated
      };
    },
    // 强制禁用缓存，确保每次筛选都会重新查询
    staleTime: 0, // 立即过期
    gcTime: 0, // 立即垃圾回收，不缓存
    retry: 1, // 减少重试次数
    refetchOnWindowFocus: false, // 禁用窗口聚焦时自动重新获取
    refetchOnMount: true, // 组件挂载时重新获取
    enabled: true // 确保查询始终启用
  });
}

export function useDailyStats() {
  return useQuery<DailyStats>({
    queryKey: ['stats'],
    queryFn: async () => {
      // Get real stats from database
      const { count: totalIdeas } = await supabase
        .from('startup_ideas')
        .select('*', { count: 'exact', head: true });

      const { count: totalIndustries } = await supabase
        .from('industries')
        .select('*', { count: 'exact', head: true });

      // Get average upvotes
      const { data: avgData } = await supabase
        .from('startup_ideas')
        .select('upvotes');
      
      const avgUpvotes = avgData && avgData.length > 0 
        ? Math.round(avgData.reduce((sum, item) => sum + (item.upvotes || 0), 0) / avgData.length)
        : 0;

      return {
        id: 1,
        date: new Date().toISOString().split('T')[0],
        totalIdeas: totalIdeas || 0,
        newIndustries: totalIndustries || 0,
        avgUpvotes: avgUpvotes,
        successRate: totalIdeas && totalIdeas > 0 ? 87.5 : 0 // Mock success rate for now
      };
    },
  });
}

// Note: Edge Functions are not deployed yet, so these are disabled for local development
// Uncomment these when Edge Functions are deployed to production

// export function useScrapeTrigger() {
//   return async () => {
//     const response = await fetch(
//       `${import.meta.env.VITE_SUPABASE_URL}/functions/v1/reddit-scraper`,
//       {
//         method: 'POST',
//         headers: {
//           'Authorization': `Bearer ${import.meta.env.VITE_SUPABASE_ANON_KEY}`,
//           'Content-Type': 'application/json',
//         },
//         body: JSON.stringify({})
//       }
//     );

//     if (!response.ok) {
//       const errorText = await response.text();
//       throw new Error(`Scraping failed: ${response.status} - ${errorText}`);
//     }

//     return response.json();
//   };
// }

// export function useAnalysisTrigger() {
//   return async (forceAnalyze = false) => {
//     const response = await fetch(
//       `${import.meta.env.VITE_SUPABASE_URL}/functions/v1/deepseek-analyzer`,
//       {
//         method: 'POST',
//         headers: {
//           'Authorization': `Bearer ${import.meta.env.VITE_SUPABASE_ANON_KEY}`,
//           'Content-Type': 'application/json',
//         },
//         body: JSON.stringify({ forceAnalyze })
//       }
//     );

//     if (!response.ok) {
//       const errorText = await response.text();
//       throw new Error(`Analysis failed: ${response.status} - ${errorText}`);
//     }

//     return response.json();
//   };
// }
