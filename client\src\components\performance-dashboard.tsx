import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { getPerformanceHistory, calculatePerformanceScore, type PerformanceData } from '@/lib/analytics';
import { TrendingUp, TrendingDown, Activity, Clock, Zap, Eye } from 'lucide-react';

interface PerformanceMetric {
  name: string;
  value: number;
  rating: 'good' | 'needs-improvement' | 'poor';
  icon: React.ReactNode;
  unit: string;
  description: string;
}

export const PerformanceDashboard: React.FC = () => {
  const [performanceData, setPerformanceData] = useState<PerformanceData[]>([]);
  const [isVisible, setIsVisible] = useState(false);

  // Only show in development environment
  const isDevelopment = import.meta.env.DEV;

  useEffect(() => {
    if (isDevelopment) {
      const data = getPerformanceHistory();
      setPerformanceData(data);
    }
  }, [isDevelopment]);

  const latestData = performanceData[performanceData.length - 1];
  const performanceScore = latestData ? calculatePerformanceScore(latestData) : 0;

  const metrics: PerformanceMetric[] = latestData ? [
    {
      name: 'LCP',
      value: latestData.lcp,
      rating: latestData.lcp <= 2500 ? 'good' : latestData.lcp <= 4000 ? 'needs-improvement' : 'poor',
      icon: <Eye className="w-4 h-4" />,
      unit: 'ms',
      description: 'Largest Contentful Paint - Time to render the largest content element'
    },
    {
      name: 'FID',
      value: latestData.fid,
      rating: latestData.fid <= 100 ? 'good' : latestData.fid <= 300 ? 'needs-improvement' : 'poor',
      icon: <Zap className="w-4 h-4" />,
      unit: 'ms',
      description: 'First Input Delay - Time from first user interaction to browser response'
    },
    {
      name: 'CLS',
      value: latestData.cls,
      rating: latestData.cls <= 0.1 ? 'good' : latestData.cls <= 0.25 ? 'needs-improvement' : 'poor',
      icon: <Activity className="w-4 h-4" />,
      unit: '',
      description: 'Cumulative Layout Shift - Visual stability of the page'
    },
    {
      name: 'FCP',
      value: latestData.fcp,
      rating: latestData.fcp <= 1800 ? 'good' : latestData.fcp <= 3000 ? 'needs-improvement' : 'poor',
      icon: <TrendingUp className="w-4 h-4" />,
      unit: 'ms',
      description: 'First Contentful Paint - Time to render the first content element'
    },
    {
      name: 'TTFB',
      value: latestData.ttfb,
      rating: latestData.ttfb <= 800 ? 'good' : latestData.ttfb <= 1800 ? 'needs-improvement' : 'poor',
      icon: <Clock className="w-4 h-4" />,
      unit: 'ms',
      description: 'Time to First Byte - Server response time'
    }
  ] : [];

  const getRatingColor = (rating: string) => {
    switch (rating) {
      case 'good': return 'text-green-400 bg-green-400/20 border-green-400/30';
      case 'needs-improvement': return 'text-yellow-400 bg-yellow-400/20 border-yellow-400/30';
      case 'poor': return 'text-red-400 bg-red-400/20 border-red-400/30';
      default: return 'text-gray-400 bg-gray-400/20 border-gray-400/30';
    }
  };

  const getScoreColor = (score: number) => {
    if (score >= 90) return 'text-green-400';
    if (score >= 70) return 'text-yellow-400';
    return 'text-red-400';
  };

  // Don't render anything in production
  if (!isDevelopment) {
    return null;
  }

  if (!isVisible) {
    return (
      <div className="fixed bottom-4 right-4 z-50">
        <Button
          onClick={() => setIsVisible(true)}
          className="bg-blue-600 hover:bg-blue-700 text-white rounded-full p-3 shadow-lg"
          title="Show Performance Dashboard"
        >
          <Activity className="w-5 h-5" />
        </Button>
      </div>
    );
  }

  return (
    <div className="fixed bottom-4 right-4 z-50 w-96 max-h-96 overflow-y-auto">
      <Card className="glass-card border-white/10 bg-black/80 backdrop-blur-md">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-white text-lg flex items-center">
              <Activity className="w-5 h-5 mr-2 text-blue-400" />
              Performance Monitor
            </CardTitle>
            <div className="flex items-center space-x-2">
              <div className={`text-2xl font-bold ${getScoreColor(performanceScore)}`}>
                {performanceScore}
              </div>
              <Button
                onClick={() => setIsVisible(false)}
                variant="ghost"
                size="sm"
                className="text-gray-400 hover:text-white p-1"
              >
                ×
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent className="space-y-3">
          {metrics.length > 0 ? (
            <>
              {metrics.map((metric) => (
                <div key={metric.name} className="flex items-center justify-between p-2 rounded-lg bg-white/5">
                  <div className="flex items-center space-x-2">
                    {metric.icon}
                    <div>
                      <div className="text-white font-medium text-sm">{metric.name}</div>
                      <div className="text-gray-400 text-xs">{metric.description}</div>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-white font-mono text-sm">
                      {metric.name === 'CLS' ? metric.value.toFixed(3) : Math.round(metric.value)}
                      {metric.unit}
                    </div>
                    <Badge className={`text-xs ${getRatingColor(metric.rating)}`}>
                      {metric.rating.replace('-', ' ')}
                    </Badge>
                  </div>
                </div>
              ))}
              
              <div className="pt-2 border-t border-white/10">
                <div className="text-xs text-gray-400 text-center">
                  Data from {performanceData.length} page loads
                </div>
              </div>
            </>
          ) : (
            <div className="text-center py-4">
              <div className="text-gray-400 text-sm">
                No performance data available yet.
                <br />
                Refresh the page to start monitoring.
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default PerformanceDashboard;
