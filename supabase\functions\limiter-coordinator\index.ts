import "jsr:@supabase/functions-js/edge-runtime.d.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface LimiterCoordinatorResponse {
  success: boolean;
  message: string;
  combinations_processed: number; // 改为组合数量而不是日期数量
  total_ideas_limited: number;
  limiter_results: Array<{
    target_date: string;
    industry_id?: number; // 新增 industry_id 字段
    success: boolean;
    ideas_deleted: number;
    industries_processed: number;
    error?: string;
  }>;
}

const LOCK_TIMEOUT = 300000; // 5分钟锁定超时

// Group array by key
function groupBy<T>(array: T[], key: keyof T): Map<any, T[]> {
  return array.reduce((groups, item) => {
    const groupKey = item[key];
    if (!groups.has(groupKey)) {
      groups.set(groupKey, []);
    }
    groups.get(groupKey)!.push(item);
    return groups;
  }, new Map());
}

Deno.serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  const supabaseClient = createClient(
    Deno.env.get('SUPABASE_URL') ?? '',
    Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
  )

  // 生成唯一锁ID
  const lockId = `limiter_coordinator_${Date.now()}_${crypto.randomUUID()}`;
  
  try {
    console.log('Limiter Coordinator: Starting idea limiting process...')

    // 1. 实现分布式锁：在scrape_tasks表中创建锁记录
    const lockExpiry = new Date(Date.now() + LOCK_TIMEOUT).toISOString();
    
    // 尝试获取锁 - 插入锁记录
    const { error: lockError } = await supabaseClient
      .from('scrape_tasks')
      .insert({
        industry_id: 1, // 使用有效的industry_id
        subreddit: 'limiter_lock', // 特殊标识：limiter锁记录
        target_date: 'lock',
        status: 'limiter_lock',
        batch_id: lockId,
        created_at: lockExpiry, // 使用created_at作为过期时间
        error_message: 'Limiter coordinator distributed lock'
      });

    // 如果插入失败，检查是否有其他活跃的锁
    if (lockError) {
      // 检查是否有未过期的锁
      const { data: existingLocks } = await supabaseClient
        .from('scrape_tasks')
        .select('id')
        .eq('status', 'limiter_lock')
        .eq('subreddit', 'limiter_lock')
        .gte('created_at', new Date().toISOString())
        .limit(1);

      if (existingLocks && existingLocks.length > 0) {
        console.log('Limiter Coordinator: Another instance is already running, skipping...')
        return new Response(
          JSON.stringify({
            success: true,
            message: 'Another limiter coordinator instance is running',
            combinations_processed: 0,
            total_ideas_limited: 0,
            limiter_results: []
          }),
          { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
        );
      }
    }

    // 2. 清理过期的锁记录
    await supabaseClient
      .from('scrape_tasks')
      .delete()
      .eq('status', 'limiter_lock')
      .eq('subreddit', 'limiter_lock')
      .lt('created_at', new Date().toISOString());

    // 3. 查找所有已完成分析的任务
    console.log('Limiter Coordinator: Checking for completed analysis tasks...');
    const { data: completedTasks, error: fetchError } = await supabaseClient
      .from('scrape_tasks')
      .select('target_date, industry_id')
      .eq('status', 'complete_analysis')
      .gt('industry_id', 0); // 排除锁记录

    if (fetchError) {
      throw new Error(`Failed to fetch completed tasks: ${fetchError.message}`);
    }

    if (!completedTasks || completedTasks.length === 0) {
      console.log('Limiter Coordinator: No completed analysis tasks found')
      return new Response(
        JSON.stringify({
          success: true,
          message: 'No completed analysis tasks to process',
          combinations_processed: 0,
          total_ideas_limited: 0,
          limiter_results: []
        }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    // 4. 按 (target_date, industry_id) 组合分组
    const industryDateGroups = new Map<string, Array<{target_date: string, industry_id: number}>>();

    for (const task of completedTasks) {
      const key = `${task.target_date}-${task.industry_id}`;
      if (!industryDateGroups.has(key)) {
        industryDateGroups.set(key, []);
      }
      industryDateGroups.get(key)!.push({
        target_date: task.target_date,
        industry_id: task.industry_id
      });
    }

    console.log(`Limiter Coordinator: Found completed tasks for ${industryDateGroups.size} different industry-date combinations`);

    const limiterResults: LimiterCoordinatorResponse['limiter_results'] = [];
    let totalIdeasLimited = 0;

    // 5. 检查每个 industry-date 组合是否需要筛选
    for (const [key, tasks] of industryDateGroups) {
      const { target_date: targetDate, industry_id: industryId } = tasks[0]; // 所有任务都有相同的 target_date 和 industry_id

      try {
        console.log(`📅🏭 Checking ${targetDate} - Industry ${industryId}...`);

        // 检查该 industry 在该日期是否还有未完成的任务
        const { data: pendingTasks } = await supabaseClient
          .from('scrape_tasks')
          .select('id')
          .eq('target_date', targetDate)
          .eq('industry_id', industryId)
          .in('status', ['pending_scrape', 'scraping', 'complete_scrape', 'analyzing']);

        if (pendingTasks && pendingTasks.length > 0) {
          console.log(`⏳ ${targetDate} - Industry ${industryId} still has ${pendingTasks.length} pending tasks, skipping limiter`);
          continue;
        }

        // 检查该 industry-date 组合是否已经筛选过
        const { data: existingLimiterRecord } = await supabaseClient
          .from('scrape_tasks')
          .select('id')
          .eq('target_date', targetDate)
          .eq('industry_id', industryId)
          .eq('limiter_processed', true)
          .limit(1);

        if (existingLimiterRecord && existingLimiterRecord.length > 0) {
          console.log(`✅ ${targetDate} - Industry ${industryId} already processed by industry limiter, skipping`);
          continue;
        }

        // 触发 industry-idea-limiter 处理特定 industry
        console.log(`🎯 Triggering industry-idea-limiter for ${targetDate} - Industry ${industryId}...`);

        const limiterResponse = await fetch(`${Deno.env.get('SUPABASE_URL')}/functions/v1/industry-idea-limiter`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            target_date: targetDate,
            industry_ids: [industryId], // 只处理这一个 industry
            max_ideas_per_industry: 10,
            preserve_subreddit_diversity: true
          })
        });

        if (limiterResponse.ok) {
          const limiterResult = await limiterResponse.json();
          console.log(`✅ Industry limiter completed for ${targetDate} - Industry ${industryId}: ${limiterResult.ideas_deleted} ideas deleted`);

          // 标记该 industry-date 组合已经处理过
          await supabaseClient
            .from('scrape_tasks')
            .update({ limiter_processed: true })
            .eq('target_date', targetDate)
            .eq('industry_id', industryId)
            .eq('status', 'complete_analysis');

          totalIdeasLimited += limiterResult.ideas_deleted;
          limiterResults.push({
            target_date: targetDate,
            industry_id: industryId,
            success: true,
            ideas_deleted: limiterResult.ideas_deleted,
            industries_processed: limiterResult.industries_processed
          });
        } else {
          const errorText = await limiterResponse.text();
          console.error(`❌ Industry limiter failed for ${targetDate} - Industry ${industryId}: ${limiterResponse.status} - ${errorText}`);

          limiterResults.push({
            target_date: targetDate,
            industry_id: industryId,
            success: false,
            ideas_deleted: 0,
            industries_processed: 0,
            error: `HTTP ${limiterResponse.status}: ${errorText}`
          });
        }

        // 添加延迟避免过于频繁的调用
        await new Promise(resolve => setTimeout(resolve, 500)); // 减少延迟，因为现在处理更细粒度

      } catch (error) {
        console.error(`❌ Error processing ${targetDate} - Industry ${industryId}:`, error);

        limiterResults.push({
          target_date: targetDate,
          industry_id: industryId,
          success: false,
          ideas_deleted: 0,
          industries_processed: 0,
          error: error.message
        });
      }
    }

    const successfulResults = limiterResults.filter(r => r.success);
    const failedResults = limiterResults.filter(r => !r.success);

    console.log(`🎉 Limiter Coordinator completed:`);
    console.log(`   🏭📅 Industry-date combinations processed: ${limiterResults.length}`);
    console.log(`   ✅ Successful: ${successfulResults.length}`);
    console.log(`   ❌ Failed: ${failedResults.length}`);
    console.log(`   🗑️ Total ideas limited: ${totalIdeasLimited}`);

    return new Response(
      JSON.stringify({
        success: true,
        message: `Successfully processed ${limiterResults.length} industry-date combinations, limited ${totalIdeasLimited} ideas`,
        combinations_processed: limiterResults.length,
        total_ideas_limited: totalIdeasLimited,
        limiter_results: limiterResults
      } as LimiterCoordinatorResponse),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );

  } catch (error) {
    console.error('❌ Limiter Coordinator Error:', error);
    
    return new Response(
      JSON.stringify({
        success: false,
        message: 'Limiter coordinator failed',
        error: error.message,
        combinations_processed: 0,
        total_ideas_limited: 0,
        limiter_results: []
      } as LimiterCoordinatorResponse),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500
      }
    );
  } finally {
    // 6. 释放锁 - 删除锁记录
    try {
      await supabaseClient
        .from('scrape_tasks')
        .delete()
        .eq('batch_id', lockId)
        .eq('status', 'limiter_lock');
    } catch (error) {
      console.error('Failed to release limiter lock:', error);
    }
  }
})
