<?xml version="1.0" encoding="UTF-8"?>
<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background with gradient from cyan to purple -->
  <defs>
    <linearGradient id="bgGradient16" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#22d3ee;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#a855f7;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Rounded rectangle background -->
  <rect x="0" y="0" width="16" height="16" rx="4" ry="4" fill="url(#bgGradient16)"/>
  
  <!-- Rocket icon (simplified for small size) -->
  <g fill="white">
    <!-- Rocket body -->
    <path d="M8 2L10 4L10 11L8 13L6 11L6 4L8 2Z"/>
    <!-- Rocket fins -->
    <path d="M6 8L4 9L4 10L6 9Z"/>
    <path d="M10 8L12 9L12 10L10 9Z"/>
    <!-- Rocket window -->
    <circle cx="8" cy="6" r="1" fill="url(#bgGradient16)"/>
    <!-- Rocket flame -->
    <path d="M7 13L8 15L9 13Z" fill="#ff6b6b"/>
  </g>
</svg>
