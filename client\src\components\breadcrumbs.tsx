import React from 'react';
import { <PERSON> } from 'wouter';
import { ChevronRight, Home } from 'lucide-react';
import { cn } from '@/lib/utils';

interface BreadcrumbItem {
  name: string;
  href?: string;
  current?: boolean;
}

interface BreadcrumbsProps {
  items: BreadcrumbItem[];
  className?: string;
  showHome?: boolean;
}

export default function Breadcrumbs({ 
  items, 
  className = '',
  showHome = true 
}: BreadcrumbsProps) {
  const allItems = showHome 
    ? [{ name: 'Home', href: '/' }, ...items]
    : items;

  return (
    <nav 
      className={cn('flex', className)} 
      aria-label="Breadcrumb"
    >
      <ol className="inline-flex items-center space-x-1 md:space-x-3">
        {allItems.map((item, index) => (
          <li key={index} className="inline-flex items-center">
            {index > 0 && (
              <ChevronRight className="w-4 h-4 text-gray-400 mx-1" />
            )}
            
            {item.href && !item.current ? (
              <Link 
                href={item.href}
                className="inline-flex items-center text-sm font-medium text-gray-400 hover:text-white transition-colors"
              >
                {index === 0 && showHome ? (
                  <>
                    <Home className="w-4 h-4 mr-2" />
                    {item.name}
                  </>
                ) : (
                  item.name
                )}
              </Link>
            ) : (
              <span 
                className={cn(
                  "inline-flex items-center text-sm font-medium",
                  item.current 
                    ? "text-white" 
                    : "text-gray-400"
                )}
                aria-current={item.current ? "page" : undefined}
              >
                {index === 0 && showHome ? (
                  <>
                    <Home className="w-4 h-4 mr-2" />
                    {item.name}
                  </>
                ) : (
                  item.name
                )}
              </span>
            )}
          </li>
        ))}
      </ol>
    </nav>
  );
}

// Specialized breadcrumb for blog posts
export function BlogBreadcrumbs({ 
  category, 
  title,
  className = '' 
}: { 
  category?: string;
  title: string;
  className?: string;
}) {
  const items: BreadcrumbItem[] = [
    { name: 'Blog', href: '/blog' }
  ];

  if (category) {
    items.push({ 
      name: category, 
      href: `/blog/category/${category.toLowerCase().replace(/\s+/g, '-')}` 
    });
  }

  items.push({ 
    name: title.length > 50 ? `${title.substring(0, 50)}...` : title, 
    current: true 
  });

  return <Breadcrumbs items={items} className={className} />;
}

// Specialized breadcrumb for idea pages
export function IdeaBreadcrumbs({ 
  industry, 
  title,
  className = '' 
}: { 
  industry?: string;
  title: string;
  className?: string;
}) {
  const items: BreadcrumbItem[] = [
    { name: 'Ideas', href: '/ideas' }
  ];

  if (industry) {
    items.push({ 
      name: industry, 
      href: `/ideas?industry=${encodeURIComponent(industry)}` 
    });
  }

  items.push({ 
    name: title.length > 40 ? `${title.substring(0, 40)}...` : title, 
    current: true 
  });

  return <Breadcrumbs items={items} className={className} />;
}

// Specialized breadcrumb for alternatives pages
export function AlternativesBreadcrumbs({ 
  tool, 
  className = '' 
}: { 
  tool: string;
  className?: string;
}) {
  const items: BreadcrumbItem[] = [
    { name: 'Alternatives', href: '/alternatives' },
    { name: `${tool} Alternatives`, current: true }
  ];

  return <Breadcrumbs items={items} className={className} />;
}

// Hook to generate breadcrumbs based on current route
export function useBreadcrumbs(pathname: string) {
  const segments = pathname.split('/').filter(Boolean);
  
  const items: BreadcrumbItem[] = [];
  let currentPath = '';

  segments.forEach((segment, index) => {
    currentPath += `/${segment}`;
    const isLast = index === segments.length - 1;
    
    // Decode URL segments
    const decodedSegment = decodeURIComponent(segment);
    
    // Format segment name
    const name = decodedSegment
      .replace(/-/g, ' ')
      .replace(/\b\w/g, l => l.toUpperCase());

    items.push({
      name,
      href: isLast ? undefined : currentPath,
      current: isLast
    });
  });

  return items;
}

// JSON-LD structured data for breadcrumbs
export function generateBreadcrumbStructuredData(items: BreadcrumbItem[]) {
  const baseUrl = 'https://ideahunter.today';
  
  return {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    "itemListElement": items.map((item, index) => ({
      "@type": "ListItem",
      "position": index + 1,
      "name": item.name,
      "item": item.href ? `${baseUrl}${item.href}` : undefined
    }))
  };
}
