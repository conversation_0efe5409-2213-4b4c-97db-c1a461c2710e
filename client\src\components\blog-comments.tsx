import React, { useState, useEffect } from 'react';
import { MessageCircle, ThumbsUp, Reply, Flag, User } from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';
import { cn } from '@/lib/utils';

interface Comment {
  id: string;
  author: string;
  email?: string;
  content: string;
  created_at: string;
  likes: number;
  replies?: Comment[];
  parent_id?: string;
  is_approved: boolean;
}

interface BlogCommentsProps {
  postSlug: string;
  postTitle: string;
}

export default function BlogComments({ postSlug, postTitle }: BlogCommentsProps) {
  const [comments, setComments] = useState<Comment[]>([]);
  const [newComment, setNewComment] = useState('');
  const [authorName, setAuthorName] = useState('');
  const [authorEmail, setAuthorEmail] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [replyingTo, setReplyingTo] = useState<string | null>(null);
  const [showCommentForm, setShowCommentForm] = useState(false);

  // Load comments (placeholder - would integrate with Supabase)
  useEffect(() => {
    loadComments();
  }, [postSlug]);

  const loadComments = async () => {
    // Placeholder for loading comments from database
    // In real implementation, this would fetch from Supabase
    const mockComments: Comment[] = [
      {
        id: '1',
        author: 'Sarah Chen',
        content: 'This is incredibly insightful! I\'ve been looking for exactly this kind of data-driven analysis. The Reddit validation approach makes so much sense.',
        created_at: '2025-01-15T10:30:00Z',
        likes: 12,
        is_approved: true,
        replies: [
          {
            id: '2',
            author: 'Mike Rodriguez',
            content: 'Totally agree! I actually found my current startup idea through a similar process on Reddit.',
            created_at: '2025-01-15T14:20:00Z',
            likes: 5,
            parent_id: '1',
            is_approved: true
          }
        ]
      },
      {
        id: '3',
        author: 'Alex Thompson',
        content: 'Great breakdown of the validation process. Would love to see more case studies like this!',
        created_at: '2025-01-16T09:15:00Z',
        likes: 8,
        is_approved: true
      }
    ];
    
    setComments(mockComments);
  };

  const handleSubmitComment = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!newComment.trim() || !authorName.trim()) return;

    setIsSubmitting(true);
    
    try {
      // Placeholder for comment submission
      // In real implementation, this would save to Supabase
      const comment: Comment = {
        id: Date.now().toString(),
        author: authorName,
        email: authorEmail,
        content: newComment,
        created_at: new Date().toISOString(),
        likes: 0,
        parent_id: replyingTo,
        is_approved: false // Comments need approval
      };

      // Add to comments list (in real app, would refetch from server)
      if (replyingTo) {
        // Add as reply
        setComments(prev => prev.map(c => 
          c.id === replyingTo 
            ? { ...c, replies: [...(c.replies || []), comment] }
            : c
        ));
      } else {
        // Add as top-level comment
        setComments(prev => [comment, ...prev]);
      }

      // Reset form
      setNewComment('');
      setAuthorName('');
      setAuthorEmail('');
      setReplyingTo(null);
      setShowCommentForm(false);
      
    } catch (error) {
      console.error('Error submitting comment:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleLikeComment = async (commentId: string) => {
    // Placeholder for like functionality
    setComments(prev => prev.map(comment => 
      comment.id === commentId 
        ? { ...comment, likes: comment.likes + 1 }
        : {
            ...comment,
            replies: comment.replies?.map(reply =>
              reply.id === commentId 
                ? { ...reply, likes: reply.likes + 1 }
                : reply
            )
          }
    ));
  };

  const CommentItem = ({ comment, isReply = false }: { comment: Comment; isReply?: boolean }) => (
    <div className={cn(
      "border border-gray-700 rounded-lg p-4 bg-gray-800",
      isReply && "ml-8 mt-3 border-gray-600"
    )}>
      <div className="flex items-start space-x-3">
        <div className="flex-shrink-0">
          <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white text-sm font-semibold">
            {comment.author.charAt(0).toUpperCase()}
          </div>
        </div>
        
        <div className="flex-1 min-w-0">
          <div className="flex items-center space-x-2 mb-2">
            <h4 className="text-sm font-semibold text-white">{comment.author}</h4>
            <span className="text-xs text-gray-400">
              {formatDistanceToNow(new Date(comment.created_at), { addSuffix: true })}
            </span>
            {!comment.is_approved && (
              <span className="text-xs bg-yellow-600 text-yellow-100 px-2 py-1 rounded">
                Pending approval
              </span>
            )}
          </div>
          
          <p className="text-gray-300 text-sm leading-relaxed mb-3">
            {comment.content}
          </p>
          
          <div className="flex items-center space-x-4 text-xs">
            <button
              onClick={() => handleLikeComment(comment.id)}
              className="flex items-center space-x-1 text-gray-400 hover:text-blue-400 transition-colors"
            >
              <ThumbsUp className="w-3 h-3" />
              <span>{comment.likes}</span>
            </button>
            
            {!isReply && (
              <button
                onClick={() => setReplyingTo(comment.id)}
                className="flex items-center space-x-1 text-gray-400 hover:text-blue-400 transition-colors"
              >
                <Reply className="w-3 h-3" />
                <span>Reply</span>
              </button>
            )}
            
            <button className="flex items-center space-x-1 text-gray-400 hover:text-red-400 transition-colors">
              <Flag className="w-3 h-3" />
              <span>Report</span>
            </button>
          </div>
        </div>
      </div>
      
      {/* Replies */}
      {comment.replies && comment.replies.length > 0 && (
        <div className="mt-4 space-y-3">
          {comment.replies.map(reply => (
            <CommentItem key={reply.id} comment={reply} isReply={true} />
          ))}
        </div>
      )}
      
      {/* Reply form */}
      {replyingTo === comment.id && (
        <div className="mt-4 ml-8">
          <CommentForm 
            onSubmit={handleSubmitComment}
            onCancel={() => setReplyingTo(null)}
            isReply={true}
          />
        </div>
      )}
    </div>
  );

  const CommentForm = ({ 
    onSubmit, 
    onCancel, 
    isReply = false 
  }: { 
    onSubmit: (e: React.FormEvent) => void;
    onCancel?: () => void;
    isReply?: boolean;
  }) => (
    <form onSubmit={onSubmit} className="space-y-4">
      {!isReply && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <input
            type="text"
            placeholder="Your name *"
            value={authorName}
            onChange={(e) => setAuthorName(e.target.value)}
            required
            className="w-full px-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
          <input
            type="email"
            placeholder="Your email (optional)"
            value={authorEmail}
            onChange={(e) => setAuthorEmail(e.target.value)}
            className="w-full px-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>
      )}
      
      <textarea
        placeholder={isReply ? "Write your reply..." : "Share your thoughts..."}
        value={newComment}
        onChange={(e) => setNewComment(e.target.value)}
        required
        rows={4}
        className="w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none"
      />
      
      <div className="flex items-center justify-between">
        <p className="text-xs text-gray-400">
          Comments are moderated and will appear after approval.
        </p>
        
        <div className="flex space-x-3">
          {onCancel && (
            <button
              type="button"
              onClick={onCancel}
              className="px-4 py-2 text-gray-400 hover:text-white transition-colors"
            >
              Cancel
            </button>
          )}
          <button
            type="submit"
            disabled={isSubmitting || !newComment.trim() || (!isReply && !authorName.trim())}
            className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            {isSubmitting ? 'Posting...' : (isReply ? 'Reply' : 'Post Comment')}
          </button>
        </div>
      </div>
    </form>
  );

  return (
    <div className="mt-12 border-t border-gray-700 pt-8">
      <div className="flex items-center justify-between mb-8">
        <h3 className="text-2xl font-bold text-white flex items-center">
          <MessageCircle className="w-6 h-6 mr-2" />
          Comments ({comments.length})
        </h3>
        
        {!showCommentForm && (
          <button
            onClick={() => setShowCommentForm(true)}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Add Comment
          </button>
        )}
      </div>

      {/* Comment form */}
      {showCommentForm && (
        <div className="mb-8 p-6 bg-gray-800 rounded-lg border border-gray-700">
          <h4 className="text-lg font-semibold text-white mb-4">Leave a Comment</h4>
          <CommentForm 
            onSubmit={handleSubmitComment}
            onCancel={() => setShowCommentForm(false)}
          />
        </div>
      )}

      {/* Comments list */}
      <div className="space-y-6">
        {comments.length > 0 ? (
          comments.map(comment => (
            <CommentItem key={comment.id} comment={comment} />
          ))
        ) : (
          <div className="text-center py-12 text-gray-400">
            <MessageCircle className="w-12 h-12 mx-auto mb-4 opacity-50" />
            <p>No comments yet. Be the first to share your thoughts!</p>
          </div>
        )}
      </div>
    </div>
  );
}
