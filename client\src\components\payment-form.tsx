import { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { ArrowLeft, CreditCard, Lock, Loader2 } from 'lucide-react';
import { loadStripe } from '@stripe/stripe-js';
import {
  Elements,
  CardElement,
  useStripe,
  useElements
} from '@stripe/react-stripe-js';
import { motion } from 'framer-motion';
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/lib/queryClient';
import PaymentSuccessModal from './payment-success-modal';

const stripePromise = loadStripe(import.meta.env.VITE_STRIPE_PUBLISHABLE_KEY);

interface PaymentFormProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onBack: () => void;
}

interface CheckoutFormProps {
  onBack: () => void;
  onSuccess: (amount: string) => void;
}

function CheckoutForm({ onBack, onSuccess }: CheckoutFormProps) {
  const stripe = useStripe();
  const elements = useElements();
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);
  const [useDiscount, setUseDiscount] = useState(true);

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();

    if (!stripe || !elements) {
      return;
    }

    setLoading(true);

    try {
      // Get the current user session
      const { data: { session } } = await supabase.auth.getSession();

      console.log('Current session:', session ? 'exists' : 'null');
      console.log('User ID:', session?.user?.id);
      console.log('Access token exists:', !!session?.access_token);

      if (!session) {
        console.error('No session found');
        toast({
          title: "Authentication Required",
          description: "Please sign in to continue with payment",
          variant: "destructive",
        });
        setLoading(false);
        return;
      }

      // Create payment intent
      const priceId = useDiscount 
        ? import.meta.env.VITE_STRIPE_PRICE_ID_DISCOUNT 
        : import.meta.env.VITE_STRIPE_PRICE_ID_ORIGINAL;

      console.log('Creating payment intent with:', {
        priceId,
        successUrl: `${window.location.origin}/payment-success`,
        cancelUrl: window.location.href,
      });

      const response = await fetch(`${import.meta.env.VITE_SUPABASE_URL}/functions/v1/stripe-payment`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session.access_token}`,
        },
        body: JSON.stringify({
          priceId,
          successUrl: `${window.location.origin}/payment-success`,
          cancelUrl: window.location.href,
        }),
      });

      console.log('Payment API response status:', response.status);
      const responseData = await response.json();
      console.log('Payment API response data:', responseData);

      const { paymentIntent, error: apiError } = responseData;

      if (apiError) {
        throw new Error(apiError);
      }

      // Confirm payment with Stripe
      const cardElement = elements.getElement(CardElement);
      
      if (!cardElement) {
        throw new Error('Card element not found');
      }

      const { error: stripeError } = await stripe.confirmCardPayment(
        paymentIntent.clientSecret,
        {
          payment_method: {
            card: cardElement,
          }
        }
      );

      if (stripeError) {
        throw new Error(stripeError.message);
      }

      console.log('Payment successful! Showing success modal...');

      toast({
        title: "Payment Successful! 🎉",
        description: "Welcome to ScraperDash Pro! You now have access to all premium features.",
      });

      // Call onSuccess with the payment amount
      const amount = useDiscount ? '4.99' : '9.99';
      onSuccess(amount);
    } catch (error) {
      console.error('Payment error:', error);
      toast({
        title: "Payment Failed",
        description: error instanceof Error ? error.message : "An unexpected error occurred",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const cardElementOptions = {
    style: {
      base: {
        fontSize: '16px',
        color: '#ffffff',
        '::placeholder': {
          color: '#9ca3af',
        },
        backgroundColor: 'transparent',
      },
      invalid: {
        color: '#ef4444',
      },
    },
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="text-center mb-6">
        <h3 className="text-xl font-semibold mb-2">Complete Your Purchase</h3>
        <div className="flex items-center justify-center space-x-4">
          <Button
            type="button"
            variant={useDiscount ? "default" : "outline"}
            onClick={() => setUseDiscount(true)}
            className={useDiscount ? "bg-cyan-500 hover:bg-cyan-600" : ""}
          >
            $4.99 (50% OFF)
          </Button>
          <Button
            type="button"
            variant={!useDiscount ? "default" : "outline"}
            onClick={() => setUseDiscount(false)}
            className={!useDiscount ? "bg-cyan-500 hover:bg-cyan-600" : ""}
          >
            $9.99 (Original)
          </Button>
        </div>
      </div>

      <div className="glass-card rounded-lg p-4 border border-gray-600">
        <div className="flex items-center mb-3">
          <CreditCard className="w-5 h-5 mr-2 text-cyan-400" />
          <span className="font-medium">Card Information</span>
        </div>
        <div className="bg-gray-800 rounded-lg p-4 border border-gray-600">
          <CardElement options={cardElementOptions} />
        </div>
      </div>

      <div className="flex items-center justify-center text-sm text-gray-400">
        <Lock className="w-4 h-4 mr-2" />
        Your payment information is secure and encrypted
      </div>

      <div className="flex space-x-3">
        <Button
          type="button"
          variant="outline"
          onClick={onBack}
          className="flex-1 border-gray-600 text-gray-300 hover:bg-gray-800"
        >
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back
        </Button>
        <Button
          type="submit"
          disabled={!stripe || loading}
          className="flex-1 bg-gradient-to-r from-cyan-500 to-purple-500 hover:from-cyan-600 hover:to-purple-600"
        >
          {loading ? (
            <>
              <Loader2 className="w-4 h-4 mr-2 animate-spin" />
              Processing...
            </>
          ) : (
            <>
              Pay ${useDiscount ? '4.99' : '9.99'}
            </>
          )}
        </Button>
      </div>
    </form>
  );
}

export default function PaymentForm({ open, onOpenChange, onBack }: PaymentFormProps) {
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [paymentAmount, setPaymentAmount] = useState<string>('');

  const handleSuccess = (amount: string) => {
    setPaymentAmount(amount);
    onOpenChange(false);
    setShowSuccessModal(true);
  };

  return (
    <>
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="max-w-md bg-gray-900 border-gray-700 text-white">
          <DialogHeader>
            <DialogTitle className="text-center">
              <span className="bg-gradient-to-r from-cyan-400 to-purple-400 bg-clip-text text-transparent">
                Upgrade to Pro
              </span>
            </DialogTitle>
          </DialogHeader>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
          >
            <Elements stripe={stripePromise}>
              <CheckoutForm onBack={onBack} onSuccess={handleSuccess} />
            </Elements>
          </motion.div>
        </DialogContent>
      </Dialog>

      <PaymentSuccessModal
        open={showSuccessModal}
        onOpenChange={setShowSuccessModal}
        amount={paymentAmount}
      />
    </>
  );
}
