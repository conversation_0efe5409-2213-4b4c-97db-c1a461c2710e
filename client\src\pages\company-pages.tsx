import { use<PERSON><PERSON><PERSON>, useLocation } from "wouter";
import { motion } from "framer-motion";
import { ArrowLeft, Building, Mail, Shield, FileText, Users, Target, Zap, Globe, Heart } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import <PERSON><PERSON><PERSON> from "@/components/seo-head";
import ParticleBackground from "@/components/particle-background";

interface PageConfig {
  title: string;
  description: string;
  icon: React.ReactNode;
  content: React.ReactNode;
  keywords: string[];
}

const PAGE_CONFIGS: Record<string, PageConfig> = {
  about: {
    title: "About IdeaHunter",
    description: "Learn about our mission to democratize startup opportunities through AI-powered Reddit analysis.",
    icon: <Building className="w-8 h-8 text-white" />,
    keywords: ["about ideahunter", "company mission", "startup discovery", "AI analysis"],
    content: (
      <div className="space-y-8">
        <div>
          <h2 className="text-2xl font-bold text-white mb-4">Our Mission</h2>
          <p className="text-gray-300 leading-relaxed">
            IdeaHunter democratizes access to startup opportunities by using AI to discover and analyze 
            trending business ideas from Reddit communities. We believe that the next big startup idea 
            could come from anywhere, and our platform helps entrepreneurs find validated opportunities 
            with real market demand.
          </p>
        </div>

        <div>
          <h2 className="text-2xl font-bold text-white mb-4">How We Work</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <Card className="glass-card border-white/10">
              <CardContent className="p-6 text-center">
                <Target className="w-12 h-12 text-blue-400 mx-auto mb-4" />
                <h3 className="text-white font-semibold mb-2">Discover</h3>
                <p className="text-gray-300 text-sm">
                  Our AI monitors 35+ Reddit communities to identify promising startup discussions
                </p>
              </CardContent>
            </Card>
            
            <Card className="glass-card border-white/10">
              <CardContent className="p-6 text-center">
                <Zap className="w-12 h-12 text-yellow-400 mx-auto mb-4" />
                <h3 className="text-white font-semibold mb-2">Analyze</h3>
                <p className="text-gray-300 text-sm">
                  Advanced algorithms assess market potential, competition, and validation signals
                </p>
              </CardContent>
            </Card>
            
            <Card className="glass-card border-white/10">
              <CardContent className="p-6 text-center">
                <Globe className="w-12 h-12 text-green-400 mx-auto mb-4" />
                <h3 className="text-white font-semibold mb-2">Share</h3>
                <p className="text-gray-300 text-sm">
                  We present curated opportunities with actionable insights for entrepreneurs
                </p>
              </CardContent>
            </Card>
          </div>
        </div>

        <div>
          <h2 className="text-2xl font-bold text-white mb-4">Our Values</h2>
          <div className="space-y-4">
            <div className="flex items-start space-x-3">
              <Heart className="w-6 h-6 text-red-400 mt-1" />
              <div>
                <h3 className="text-white font-semibold">Community-Driven</h3>
                <p className="text-gray-300">We believe the best ideas come from real people discussing real problems.</p>
              </div>
            </div>
            <div className="flex items-start space-x-3">
              <Shield className="w-6 h-6 text-blue-400 mt-1" />
              <div>
                <h3 className="text-white font-semibold">Data-Driven</h3>
                <p className="text-gray-300">Every insight is backed by quantitative analysis and community validation.</p>
              </div>
            </div>
            <div className="flex items-start space-x-3">
              <Users className="w-6 h-6 text-purple-400 mt-1" />
              <div>
                <h3 className="text-white font-semibold">Accessible</h3>
                <p className="text-gray-300">We make startup opportunities accessible to entrepreneurs everywhere.</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    )
  },

  contact: {
    title: "Contact Us",
    description: "Get in touch with the IdeaHunter team for support, partnerships, or feedback.",
    icon: <Mail className="w-8 h-8 text-white" />,
    keywords: ["contact ideahunter", "support", "feedback", "partnerships"],
    content: (
      <div className="space-y-8">
        <div>
          <h2 className="text-2xl font-bold text-white mb-4">Get in Touch</h2>
          <p className="text-gray-300 leading-relaxed mb-6">
            We'd love to hear from you! Whether you have questions, feedback, or partnership opportunities, 
            our team is here to help.
          </p>
        </div>

        <div className="max-w-md mx-auto">
          <Card className="glass-card border-white/10">
            <CardHeader>
              <CardTitle className="text-white flex items-center">
                <Mail className="w-5 h-5 mr-2 text-blue-400" />
                General Inquiries
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-300 mb-4">
                For general questions about IdeaHunter, our platform, or startup opportunities.
              </p>
              <Button
                className="w-full bg-blue-500 hover:bg-blue-600"
                onClick={() => window.location.href = 'mailto:<EMAIL>'}
              >
                <EMAIL>
              </Button>
            </CardContent>
          </Card>
        </div>

        <div>
          <h2 className="text-2xl font-bold text-white mb-4">Frequently Asked Questions</h2>
          <p className="text-gray-300 mb-4">
            Before reaching out, you might find answers to common questions in our FAQ section.
          </p>
          <Button 
            variant="outline" 
            className="border-white/20 text-white hover:bg-white/10"
            onClick={() => window.location.href = '/faq'}
          >
            Visit FAQ
          </Button>
        </div>
      </div>
    )
  },

  privacy: {
    title: "Privacy Policy",
    description: "Learn how IdeaHunter collects, uses, and protects your personal information.",
    icon: <Shield className="w-8 h-8 text-white" />,
    keywords: ["privacy policy", "data protection", "user privacy", "GDPR"],
    content: (
      <div className="space-y-8">
        <div>
          <p className="text-gray-300 leading-relaxed">
            <strong>Last updated:</strong> January 2025
          </p>
        </div>

        <div>
          <h2 className="text-2xl font-bold text-white mb-4">Information We Collect</h2>
          <div className="space-y-4 text-gray-300">
            <div>
              <h3 className="text-white font-semibold mb-2">Account Information</h3>
              <p>When you create an account, we collect your email address and basic profile information through Google OAuth.</p>
            </div>
            <div>
              <h3 className="text-white font-semibold mb-2">Usage Data</h3>
              <p>We collect information about how you use our platform, including pages visited, features used, and interaction patterns.</p>
            </div>
            <div>
              <h3 className="text-white font-semibold mb-2">Public Reddit Data</h3>
              <p>We analyze publicly available Reddit posts and comments to identify startup opportunities. No personal Reddit data is stored.</p>
            </div>
          </div>
        </div>

        <div>
          <h2 className="text-2xl font-bold text-white mb-4">How We Use Your Information</h2>
          <ul className="space-y-2 text-gray-300">
            <li>• Provide and improve our services</li>
            <li>• Personalize your experience</li>
            <li>• Send important updates and notifications</li>
            <li>• Analyze platform usage and performance</li>
            <li>• Ensure security and prevent fraud</li>
          </ul>
        </div>

        <div>
          <h2 className="text-2xl font-bold text-white mb-4">Data Security</h2>
          <p className="text-gray-300 leading-relaxed">
            We implement industry-standard security measures to protect your personal information. 
            All data is encrypted in transit and at rest. We use Supabase for secure data storage 
            and authentication.
          </p>
        </div>

        <div>
          <h2 className="text-2xl font-bold text-white mb-4">Your Rights</h2>
          <p className="text-gray-300 leading-relaxed">
            You have the right to access, update, or delete your personal information. 
            You can also opt out of non-essential communications at any time. 
            Contact <NAME_EMAIL> for any privacy-related requests.
          </p>
        </div>
      </div>
    )
  },

  terms: {
    title: "Terms of Service",
    description: "Terms and conditions for using the IdeaHunter platform and services.",
    icon: <FileText className="w-8 h-8 text-white" />,
    keywords: ["terms of service", "user agreement", "platform rules", "legal"],
    content: (
      <div className="space-y-8">
        <div>
          <p className="text-gray-300 leading-relaxed">
            <strong>Last updated:</strong> January 2025
          </p>
        </div>

        <div>
          <h2 className="text-2xl font-bold text-white mb-4">Acceptance of Terms</h2>
          <p className="text-gray-300 leading-relaxed">
            By accessing and using IdeaHunter, you accept and agree to be bound by the terms 
            and provision of this agreement. If you do not agree to abide by the above, 
            please do not use this service.
          </p>
        </div>

        <div>
          <h2 className="text-2xl font-bold text-white mb-4">Use License</h2>
          <div className="space-y-4 text-gray-300">
            <p>Permission is granted to temporarily access IdeaHunter for personal, non-commercial transitory viewing only. This includes:</p>
            <ul className="space-y-2 ml-4">
              <li>• Viewing startup ideas and market analysis</li>
              <li>• Using our search and filtering features</li>
              <li>• Accessing educational content and resources</li>
            </ul>
            <p>This license shall automatically terminate if you violate any of these restrictions.</p>
          </div>
        </div>

        <div>
          <h2 className="text-2xl font-bold text-white mb-4">Disclaimer</h2>
          <p className="text-gray-300 leading-relaxed">
            The information on IdeaHunter is provided on an 'as is' basis. To the fullest extent 
            permitted by law, this Company excludes all representations, warranties, conditions 
            and terms relating to our website and the use of this website. Startup ideas and 
            market analysis are for informational purposes only and should not be considered 
            as investment or business advice.
          </p>
        </div>

        <div>
          <h2 className="text-2xl font-bold text-white mb-4">Limitations</h2>
          <p className="text-gray-300 leading-relaxed">
            In no event shall IdeaHunter or its suppliers be liable for any damages arising 
            out of the use or inability to use the materials on our platform, even if we 
            have been notified orally or in writing of the possibility of such damage.
          </p>
        </div>

        <div>
          <h2 className="text-2xl font-bold text-white mb-4">Contact Information</h2>
          <p className="text-gray-300 leading-relaxed">
            If you have any questions about these Terms of Service, please contact us at 
            <EMAIL>.
          </p>
        </div>
      </div>
    )
  }
};

interface CompanyPagesProps {
  params?: { page: string };
}

export default function CompanyPages({ params }: CompanyPagesProps = {}) {
  const urlParams = useParams();
  const [, setLocation] = useLocation();

  // Use props params if provided, otherwise use URL params
  const page = (params?.page || urlParams.page) as keyof typeof PAGE_CONFIGS;
  const config = PAGE_CONFIGS[page];

  if (!config) {
    setLocation('/404');
    return null;
  }

  return (
    <>
      <SEOHead 
        title={`${config.title} | IdeaHunter`}
        description={config.description}
        keywords={config.keywords}
        type="website"
      />
      
      <div className="bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 relative overflow-hidden min-h-screen">
        <ParticleBackground />
        
        <div className="relative z-10 container mx-auto px-4 py-8">
          <div className="max-w-4xl mx-auto">
            
            {/* Header */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="mb-8"
            >
              <Button
                onClick={() => setLocation('/')}
                variant="ghost"
                className="mb-6 text-gray-400 hover:text-white transition-colors"
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Dashboard
              </Button>

              <div className="flex items-center space-x-4 mb-6">
                <div className="w-16 h-16 rounded-xl bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center">
                  {config.icon}
                </div>
                <div>
                  <h1 className="text-4xl md:text-5xl font-bold text-white mb-2">
                    {config.title}
                  </h1>
                  <p className="text-xl text-gray-300">
                    {config.description}
                  </p>
                </div>
              </div>
            </motion.div>

            {/* Content */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 }}
            >
              <Card className="glass-card border-white/10">
                <CardContent className="p-8">
                  {config.content}
                </CardContent>
              </Card>
            </motion.div>

          </div>
        </div>
      </div>
    </>
  );
}
