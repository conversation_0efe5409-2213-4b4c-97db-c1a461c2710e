import "jsr:@supabase/functions-js/edge-runtime.d.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface LimiterRequest {
  target_date: string;                    // YYYY-MM-DD format
  industry_ids?: number[];                // 可选，指定要处理的行业
  max_ideas_per_industry?: number;        // 默认10
  preserve_subreddit_diversity?: boolean; // 是否保持subreddit多样性，默认true
}

interface LimiterResponse {
  success: boolean;
  message: string;
  target_date: string;
  industries_processed: number;
  total_ideas_before: number;
  total_ideas_after: number;
  ideas_deleted: number;
  industry_results: Array<{
    industry_id: number;
    industry_name: string;
    ideas_before: number;
    ideas_after: number;
    ideas_deleted: number;
    subreddits_represented: string[];
  }>;
}

interface StartupIdea {
  id: number;
  title: string;
  summary: string;
  industry_id: number;
  upvotes: number;
  comments: number;
  keywords: string[];
  subreddit: string;
  reddit_post_urls: string[];
  existing_solutions: string;
  solution_gaps: string;
  market_size: string;
  target_date: string;
  confidence_score: number;
  quality_score: number;
  innovation_score: number;
  created_at: string;
}

// Industry mapping - using English names to match database
const INDUSTRY_MAPPING = {
  1: 'SaaS & Cloud Services',

  4: 'Mobile App Development',
  5: 'Web & Frontend Development',
  6: 'No-Code/Low-Code Platforms',

  8: 'AI & Machine Learning',
  9: 'E-commerce & Retail',
  10: 'Health & Fitness Tech',
  11: 'EdTech',
  12: 'FinTech',
  13: 'Startup & Business',
  14: 'Consumer Services & Freelance',
  15: 'Enterprise & B2B Services',
  16: 'Digital Marketing & SEO',
  17: 'Social Media Marketing & Influencers',
  18: 'Media & Content Creation',
  19: 'Photography & Visual Arts',
  20: 'Design & Creative Tools',
  21: 'Travel & Transportation',
  22: 'GreenTech & Sustainability',
  23: 'Logistics & Supply Chain',
  24: 'Gaming & Entertainment',

  26: 'AR/VR & Metaverse',
  27: 'BioTech & MedTech',
  28: 'LegalTech',
  29: 'PropTech',
  30: 'Data Science & Analytics',
  31: 'Blockchain & Cryptocurrency',
  32: 'Stock Investment & Trading',
  33: 'Financial Independence & Personal Finance',
  34: 'Audio & Podcast',
  35: 'AgTech',
  36: 'Pet Care & Community',
  37: 'Family & Parenting',
  38: 'General/Trending Topics',
  39: 'MRR showcase'
};

// Group array by key
function groupBy<T>(array: T[], key: keyof T): Map<any, T[]> {
  return array.reduce((groups, item) => {
    const groupKey = item[key];
    if (!groups.has(groupKey)) {
      groups.set(groupKey, []);
    }
    groups.get(groupKey)!.push(item);
    return groups;
  }, new Map());
}

// Select ideas with subreddit diversity
function selectWithDiversity(ideas: StartupIdea[], maxCount: number): StartupIdea[] {
  if (ideas.length <= maxCount) {
    return ideas;
  }

  // Group by subreddit
  const subredditGroups = groupBy(ideas, 'subreddit');
  const selected: StartupIdea[] = [];
  
  // First round: select highest quality idea from each subreddit
  for (const [subreddit, subredditIdeas] of subredditGroups) {
    if (selected.length < maxCount) {
      // Ideas are already sorted by quality
      selected.push(subredditIdeas[0]);
    }
  }
  
  // Second round: fill remaining slots with highest quality ideas
  if (selected.length < maxCount) {
    const remaining = ideas.filter(idea => !selected.some(s => s.id === idea.id));
    const remainingSlots = maxCount - selected.length;
    selected.push(...remaining.slice(0, remainingSlots));
  }
  
  // Sort final selection by quality score
  return selected.sort((a, b) => b.quality_score - a.quality_score);
}

// Main limiter function
async function limitIdeasForIndustries(
  supabaseClient: any,
  targetDate: string,
  industryIds?: number[],
  maxIdeasPerIndustry: number = 10,
  preserveSubredditDiversity: boolean = true
): Promise<LimiterResponse> {
  console.log(`🎯 Starting industry idea limiter for ${targetDate}...`);
  
  // Build query
  let query = supabaseClient
    .from('startup_ideas')
    .select('*')
    .eq('target_date', targetDate);
    
  if (industryIds && industryIds.length > 0) {
    query = query.in('industry_id', industryIds);
    console.log(`🔍 Filtering for industries: ${industryIds.join(', ')}`);
  }
  
  const { data: allIdeas, error: fetchError } = await query
    .order('quality_score', { ascending: false })
    .order('confidence_score', { ascending: false })
    .order('innovation_score', { ascending: false });
    
  if (fetchError) {
    throw new Error(`Failed to fetch ideas: ${fetchError.message}`);
  }
  
  if (!allIdeas || allIdeas.length === 0) {
    console.log(`📭 No ideas found for ${targetDate}`);
    return {
      success: true,
      message: `No ideas found for ${targetDate}`,
      target_date: targetDate,
      industries_processed: 0,
      total_ideas_before: 0,
      total_ideas_after: 0,
      ideas_deleted: 0,
      industry_results: []
    };
  }
  
  console.log(`📊 Found ${allIdeas.length} ideas for ${targetDate}`);
  
  // Group by industry
  const industryGroups = groupBy(allIdeas, 'industry_id');
  const industryResults: LimiterResponse['industry_results'] = [];
  let totalDeleted = 0;
  
  // Process each industry
  for (const [industryId, ideas] of industryGroups) {
    const industryName = INDUSTRY_MAPPING[industryId as keyof typeof INDUSTRY_MAPPING] || `Industry ${industryId}`;
    const ideasBefore = ideas.length;

    // Special limit for MRR showcase industry (ID: 39)
    const currentMaxIdeas = industryId === 39 ? 15 : maxIdeasPerIndustry;

    console.log(`🏭 Processing ${industryName} (ID: ${industryId}): ${ideasBefore} ideas (limit: ${currentMaxIdeas})`);

    if (ideasBefore <= currentMaxIdeas) {
      console.log(`✅ ${industryName}: ${ideasBefore} ideas (within limit of ${currentMaxIdeas})`);

      industryResults.push({
        industry_id: industryId as number,
        industry_name: industryName,
        ideas_before: ideasBefore,
        ideas_after: ideasBefore,
        ideas_deleted: 0,
        subreddits_represented: [...new Set(ideas.map(i => i.subreddit))]
      });
      continue;
    }

    // Select ideas to keep
    const selectedIdeas = preserveSubredditDiversity
      ? selectWithDiversity(ideas, currentMaxIdeas)
      : ideas.slice(0, currentMaxIdeas);
      
    const ideasToDelete = ideas.filter(idea => !selectedIdeas.some(s => s.id === idea.id));
    const deletedCount = ideasToDelete.length;
    
    if (deletedCount > 0) {
      // Delete excess ideas
      const idsToDelete = ideasToDelete.map(idea => idea.id);
      const { error: deleteError } = await supabaseClient
        .from('startup_ideas')
        .delete()
        .in('id', idsToDelete);
        
      if (deleteError) {
        console.error(`❌ Error deleting ideas for ${industryName}:`, deleteError);
        throw new Error(`Failed to delete ideas for ${industryName}: ${deleteError.message}`);
      }
      
      console.log(`🗑️ ${industryName}: Deleted ${deletedCount} ideas, kept ${selectedIdeas.length}`);
      totalDeleted += deletedCount;
    }
    
    industryResults.push({
      industry_id: industryId as number,
      industry_name: industryName,
      ideas_before: ideasBefore,
      ideas_after: selectedIdeas.length,
      ideas_deleted: deletedCount,
      subreddits_represented: [...new Set(selectedIdeas.map(i => i.subreddit))]
    });
  }
  
  const totalAfter = allIdeas.length - totalDeleted;
  
  console.log(`🎉 Industry limiter completed for ${targetDate}:`);
  console.log(`   📊 Industries processed: ${industryGroups.size}`);
  console.log(`   💡 Ideas before: ${allIdeas.length}`);
  console.log(`   💡 Ideas after: ${totalAfter}`);
  console.log(`   🗑️ Ideas deleted: ${totalDeleted}`);
  
  return {
    success: true,
    message: `Successfully limited ideas for ${industryGroups.size} industries on ${targetDate}`,
    target_date: targetDate,
    industries_processed: industryGroups.size,
    total_ideas_before: allIdeas.length,
    total_ideas_after: totalAfter,
    ideas_deleted: totalDeleted,
    industry_results: industryResults
  };
}

Deno.serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const {
      target_date,
      industry_ids,
      max_ideas_per_industry = 10,
      preserve_subreddit_diversity = true
    }: LimiterRequest = await req.json();

    // Validate required parameters
    if (!target_date) {
      throw new Error('target_date is required');
    }

    // Validate date format
    if (!/^\d{4}-\d{2}-\d{2}$/.test(target_date)) {
      throw new Error('target_date must be in YYYY-MM-DD format');
    }

    console.log(`🚀 Industry Idea Limiter triggered for ${target_date}`);
    console.log(`   🎯 Max ideas per industry: ${max_ideas_per_industry}`);
    console.log(`   🌈 Preserve subreddit diversity: ${preserve_subreddit_diversity}`);
    if (industry_ids && industry_ids.length > 0) {
      if (industry_ids.length === 1) {
        console.log(`   🏭 Target industry: ${industry_ids[0]} (single industry mode)`);
      } else {
        console.log(`   🏭 Target industries: ${industry_ids.join(', ')} (${industry_ids.length} industries)`);
      }
    } else {
      console.log(`   🏭 Processing all industries for this date`);
    }
    
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    );

    const result = await limitIdeasForIndustries(
      supabaseClient,
      target_date,
      industry_ids,
      max_ideas_per_industry,
      preserve_subreddit_diversity
    );

    return new Response(
      JSON.stringify(result),
      {
        status: 200,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    );

  } catch (error) {
    console.error('❌ Industry Idea Limiter failed:', error);
    return new Response(
      JSON.stringify({
        success: false,
        message: 'Industry idea limiter failed',
        error: error.message,
        target_date: '',
        industries_processed: 0,
        total_ideas_before: 0,
        total_ideas_after: 0,
        ideas_deleted: 0,
        industry_results: []
      } as LimiterResponse),
      {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    );
  }
})
