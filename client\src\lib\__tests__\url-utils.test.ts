import { createSlug, getIdeaUrl, getIdeaShareUrl, extractIdeaId, validateSlug } from '../url-utils';

describe('URL Utils', () => {
  describe('createSlug', () => {
    it('should create a basic slug', () => {
      expect(createSlug('Hello World')).toBe('hello-world');
    });

    it('should handle special characters', () => {
      expect(createSlug('AI-Powered SaaS Platform!')).toBe('ai-powered-saas-platform');
    });

    it('should handle multiple spaces and hyphens', () => {
      expect(createSlug('Multiple   Spaces---And___Underscores')).toBe('multiple-spaces-and-underscores');
    });

    it('should limit length to 60 characters', () => {
      const longTitle = 'This is a very long title that should be truncated to sixty characters maximum';
      const slug = createSlug(longTitle);
      expect(slug.length).toBeLessThanOrEqual(60);
      expect(slug).toBe('this-is-a-very-long-title-that-should-be-truncated-to');
    });

    it('should remove leading and trailing hyphens', () => {
      expect(createSlug('---Hello World---')).toBe('hello-world');
    });
  });

  describe('getIdeaUrl', () => {
    it('should generate correct idea URL', () => {
      expect(getIdeaUrl(123, 'AI-Powered Productivity Tool')).toBe('/idea/123/ai-powered-productivity-tool');
    });
  });

  describe('getIdeaShareUrl', () => {
    it('should generate absolute URL with custom base', () => {
      expect(getIdeaShareUrl(123, 'Test Idea', 'https://example.com')).toBe('https://example.com/idea/123/test-idea');
    });
  });

  describe('extractIdeaId', () => {
    it('should extract valid ID', () => {
      expect(extractIdeaId('123')).toBe(123);
    });

    it('should return undefined for invalid ID', () => {
      expect(extractIdeaId('abc')).toBeUndefined();
      expect(extractIdeaId(undefined)).toBeUndefined();
    });
  });

  describe('validateSlug', () => {
    it('should validate correct slugs', () => {
      expect(validateSlug('hello-world')).toBe(true);
      expect(validateSlug('ai-powered-tool-123')).toBe(true);
    });

    it('should reject invalid slugs', () => {
      expect(validateSlug('Hello World')).toBe(false); // spaces
      expect(validateSlug('hello_world')).toBe(false); // underscores
      expect(validateSlug('hello@world')).toBe(false); // special chars
    });

    it('should reject slugs that are too long', () => {
      const longSlug = 'a'.repeat(61);
      expect(validateSlug(longSlug)).toBe(false);
    });
  });
});
