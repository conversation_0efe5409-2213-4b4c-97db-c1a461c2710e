
import "jsr:@supabase/functions-js/edge-runtime.d.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface ScraperTask {
  id: number;
  industry_id: number;
  subreddit: string;
  target_date: string;
  batch_id: string;
  retry_count: number;
  max_retries: number;
}

const CONCURRENT_SUBREDDITS = 3; // 最多3个并发subreddit处理
const LOCK_TIMEOUT = 300000; // 5分钟锁定超时
const TASK_TIMEOUT = 300000; // 5分钟任务超时
const MAX_TASK_RETRIES = 3; // 最大重试次数

Deno.serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  const supabaseClient = createClient(
    Deno.env.get('SUPABASE_URL') ?? '',
    Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
  )

  // 生成唯一锁ID
  const lockId = `scraper_coordinator_${Date.now()}_${crypto.randomUUID()}`;
  
  try {
    console.log('Scraper Coordinator: Starting task processing...')

    // 1. 实现真正的分布式锁：在scrape_tasks表中创建锁记录
    const lockExpiry = new Date(Date.now() + LOCK_TIMEOUT).toISOString();
    
    // 尝试获取锁 - 插入锁记录
    const { error: lockError } = await supabaseClient
      .from('scrape_tasks')
      .insert({
        industry_id: -1, // 特殊标识：锁记录
        target_date: 'lock',
        status: 'coordinator_lock',
        batch_id: lockId,
        created_at: lockExpiry, // 使用created_at作为过期时间
        error_message: 'Scraper coordinator distributed lock'
      });

    // 如果插入失败，检查是否有其他活跃的锁
    if (lockError) {
      // 检查是否有未过期的锁
      const { data: existingLocks } = await supabaseClient
        .from('scrape_tasks')
        .select('id')
        .eq('status', 'coordinator_lock')
        .eq('industry_id', -1)
        .gte('created_at', new Date().toISOString())
        .limit(1);

      if (existingLocks && existingLocks.length > 0) {
        console.log('Scraper Coordinator: Another instance is already running, skipping...')
        return new Response(
          JSON.stringify({ 
            success: true, 
            message: 'Another coordinator instance is running',
            tasksProcessed: 0
          }),
          { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
        );
      }
    }

    // 2. 清理过期的锁记录
    await supabaseClient
      .from('scrape_tasks')
      .delete()
      .eq('status', 'coordinator_lock')
      .eq('industry_id', -1)
      .lt('created_at', new Date().toISOString());

    // 3. 检查并重置超时任务
    console.log('Scraper Coordinator: Checking for timeout tasks...');
    const { data: timeoutTasks, error: timeoutError } = await supabaseClient
      .from('scrape_tasks')
      .select('id, retry_count, max_retries, industry_id, started_at, batch_id')
      .eq('status', 'scraping')
      .lt('started_at', new Date(Date.now() - TASK_TIMEOUT).toISOString());

    if (timeoutError) {
      console.error('Error checking timeout tasks:', timeoutError);
    }

    if (timeoutTasks && timeoutTasks.length > 0) {
      console.log(`⚠️ Scraper Coordinator: Found ${timeoutTasks.length} timeout tasks, analyzing...`);
      
      // 详细记录超时任务信息
      timeoutTasks.forEach(task => {
        const timeoutDuration = Date.now() - new Date(task.started_at).getTime();
        const timeoutMinutes = Math.floor(timeoutDuration / 60000);
        console.log(`⏰ Timeout Task Details:`, {
          taskId: task.id,
          industryId: task.industry_id,
          batchId: task.batch_id,
          startedAt: task.started_at,
          timeoutMinutes: timeoutMinutes,
          retryCount: task.retry_count,
          maxRetries: task.max_retries
        });
      });
      
      // 重置未超过最大重试次数的超时任务
      const retryableTimeoutTasks = timeoutTasks.filter(task => 
        task.retry_count < (task.max_retries || MAX_TASK_RETRIES)
      );
      const failedTimeoutTasks = timeoutTasks.filter(task => 
        task.retry_count >= (task.max_retries || MAX_TASK_RETRIES)
      );
      
      console.log(`🔄 Retryable timeout tasks: ${retryableTimeoutTasks.length}, Failed timeout tasks: ${failedTimeoutTasks.length}`);
      
      if (retryableTimeoutTasks.length > 0) {
        for (const task of retryableTimeoutTasks) {
          const newRetryCount = task.retry_count + 1;
          console.log(`🔁 Retrying timeout task ${task.id} (attempt ${newRetryCount}/${task.max_retries || MAX_TASK_RETRIES})`);
          
          await supabaseClient
            .from('scrape_tasks')
            .update({
              status: 'pending_scrape',
              retry_count: newRetryCount,
              error_message: `Task timeout after ${Math.floor(TASK_TIMEOUT/60000)} minutes, retrying... (attempt ${newRetryCount})`,
              started_at: null
            })
            .eq('id', task.id);
        }
      }
      
      if (failedTimeoutTasks.length > 0) {
        console.log(`❌ Marking ${failedTimeoutTasks.length} tasks as failed due to max retries exceeded`);
        
        await supabaseClient
          .from('scrape_tasks')
          .update({
            status: 'failed',
            error_message: `Task timeout after multiple retries. Max timeout: ${Math.floor(TASK_TIMEOUT/60000)} minutes, Max retries: ${MAX_TASK_RETRIES}`,
            completed_at: new Date().toISOString()
          })
          .in('id', failedTimeoutTasks.map(t => t.id));
      }
    }

    // 4. 检查并重试失败的任务（30分钟后重试，优化间隔）
    console.log('Scraper Coordinator: Checking for retryable failed tasks...');
    const { data: retryTasks } = await supabaseClient
      .from('scrape_tasks')
      .select('*')
      .eq('status', 'failed')
      .filter('retry_count', 'lt', MAX_TASK_RETRIES)
      .lt('completed_at', new Date(Date.now() - 1800000).toISOString()); // 30分钟后重试 (优化间隔)

    if (retryTasks && retryTasks.length > 0) {
      console.log(`🔄 Scraper Coordinator: Found ${retryTasks.length} retryable failed tasks`);
      
      for (const task of retryTasks) {
        const newRetryCount = task.retry_count + 1;
        console.log(`🔁 Retrying failed task ${task.id} (industry ${task.industry_id}, attempt ${newRetryCount}/${MAX_TASK_RETRIES})`);
        
        await supabaseClient
          .from('scrape_tasks')
          .update({
            status: 'pending_scrape',
            retry_count: newRetryCount,
            error_message: `Retrying failed task (attempt ${newRetryCount}/${MAX_TASK_RETRIES})`,
            started_at: null,
            completed_at: null
          })
          .eq('id', task.id);
      }
    }

    // 5. 检查正在运行的任务数量
    console.log('Scraper Coordinator: Checking for running scraping tasks...');
    const { data: runningTasks, error: runningError } = await supabaseClient
      .from('scrape_tasks')
      .select('id')
      .eq('status', 'scraping')
      .gt('industry_id', 0); // 排除锁记录

    if (runningError) {
      throw new Error(`Failed to check running tasks: ${runningError.message}`);
    }

    const runningTaskCount = runningTasks?.length || 0;
    console.log(`Scraper Coordinator: Found ${runningTaskCount} running scraping tasks`);

    // 如果已经有2个或更多任务在运行，不处理新任务
    if (runningTaskCount >= 2) {
      console.log('Scraper Coordinator: Maximum concurrent tasks reached, skipping new task processing')
      return new Response(
        JSON.stringify({ 
          success: true, 
          message: `Maximum concurrent tasks reached (${runningTaskCount}/2), skipping new task processing`,
          tasksProcessed: 0,
          runningTasks: runningTaskCount
        }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    // 6. 获取待处理的subreddit任务，根据剩余容量限制
    const availableSlots = CONCURRENT_SUBREDDITS - runningTaskCount;
    const taskLimit = Math.min(CONCURRENT_SUBREDDITS, availableSlots);

    console.log(`Scraper Coordinator: Available subreddit slots: ${availableSlots}, will process up to ${taskLimit} subreddit tasks`);

    const { data: pendingTasks, error: fetchError } = await supabaseClient
      .from('scrape_tasks')
      .select(`
        id,
        industry_id,
        subreddit,
        target_date,
        batch_id,
        retry_count,
        max_retries
      `)
      .eq('status', 'pending_scrape')
      .gt('industry_id', 0) // 排除锁记录
      .order('created_at', { ascending: true })
      .limit(taskLimit);

    if (fetchError) {
      throw new Error(`Failed to fetch pending tasks: ${fetchError.message}`);
    }

    if (!pendingTasks || pendingTasks.length === 0) {
      console.log('Scraper Coordinator: No pending tasks found')
      return new Response(
        JSON.stringify({ 
          success: true, 
          message: 'No pending tasks to process',
          tasksProcessed: 0,
          runningTasks: runningTaskCount
        }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    console.log(`Scraper Coordinator: Found ${pendingTasks.length} pending subreddit tasks, will process with ${runningTaskCount} already running`);

    // 7. 处理subreddit级别的任务 - 每个任务对应一个subreddit
    // 按最多3个subreddit分组进行并发处理
    const maxSubredditsPerBatch = 3; // 每批最多处理3个subreddit
    const taskBatches = [];

    for (let i = 0; i < pendingTasks.length; i += maxSubredditsPerBatch) {
      taskBatches.push(pendingTasks.slice(i, i + maxSubredditsPerBatch));
    }

    const totalBatches = taskBatches.length;
    console.log(`📊 Scraper Coordinator: Processing ${pendingTasks.length} subreddit tasks in ${totalBatches} batches (max ${maxSubredditsPerBatch} subreddits per batch)`);

    let processedCount = 0;
    let successCount = 0;
    let errorCount = 0;

    // 分批处理subreddit任务
    for (let batchIndex = 0; batchIndex < totalBatches; batchIndex++) {
      const batchTasks = taskBatches[batchIndex];
      const subreddits = batchTasks.map(task => task.subreddit);

      console.log(`🔄 Processing batch ${batchIndex + 1}/${totalBatches}: ${subreddits.join(', ')}`);

      // 8. 使用事务更新当前批次任务状态为 'scraping'
      const batchTaskIds = batchTasks.map(task => task.id);
      const { error: updateError } = await supabaseClient
        .from('scrape_tasks')
        .update({
          status: 'scraping',
          started_at: new Date().toISOString()
        })
        .in('id', batchTaskIds)
        .eq('status', 'pending_scrape'); // 添加状态检查防止竞争条件

      if (updateError) {
        console.error(`❌ Failed to update batch ${batchIndex + 1} task status:`, updateError.message);
        errorCount += batchTasks.length;
        continue;
      }

      // 9. 准备调用 reddit-scraper 的参数 (现在是subreddit级别)
      const targetDate = batchTasks[0].target_date;
      const batchIds = [...new Set(batchTasks.map(task => task.batch_id))];

      const scraperPayload = {
        subreddits: subreddits,
        target_date: targetDate,
        task_ids: batchTaskIds,
        batch_id: `${batchIds[0]}_batch_${batchIndex + 1}`
      };

      console.log(`📤 Batch ${batchIndex + 1}: Calling reddit-scraper with payload:`, {
        subreddits: scraperPayload.subreddits.join(', '),
        task_ids: scraperPayload.task_ids.length + ' tasks',
        target_date: scraperPayload.target_date
      });

      // 10. Fire-and-forget调用 reddit-scraper 函数
      console.log(`🚀 Batch ${batchIndex + 1}: Triggering reddit-scraper (fire-and-forget)...`);

      try {
        // Fire-and-forget: 不等待响应，立即继续处理下一批
        fetch(`${Deno.env.get('SUPABASE_URL')}/functions/v1/reddit-scraper`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(scraperPayload)
        }).catch(error => {
          console.error(`❌ Batch ${batchIndex + 1}: Fire-and-forget call failed:`, error);
          // 在fire-and-forget模式下，我们不能直接处理错误
          // reddit-scraper会自己处理错误并更新任务状态
        });

        console.log(`🚀 Batch ${batchIndex + 1}: Successfully triggered reddit-scraper (fire-and-forget)`);
        successCount += batchTasks.length;

      } catch (error) {
        console.error(`❌ Batch ${batchIndex + 1}: Error triggering reddit-scraper:`, error);

        // 网络错误或其他异常，重置任务状态
        await supabaseClient
          .from('scrape_tasks')
          .update({
            status: 'pending_scrape',
            error_message: `Network error: ${error.message}`,
            started_at: null
          })
          .in('id', batchTaskIds);

        errorCount += batchTasks.length;
      }

      processedCount += batchTasks.length;

      // 11. 批次间智能延迟 - 避免API限流
      if (batchIndex < totalBatches - 1) {
        const batchDelay = 60000; // 批次间延迟60秒，给Reddit API充分时间恢复
        console.log(`⏳ Waiting ${batchDelay/1000}s before processing next batch...`);
        await new Promise(resolve => setTimeout(resolve, batchDelay));
      }
    }

    console.log(`🎉 Scraper Coordinator completed: ${processedCount} tasks processed, ${successCount} successful, ${errorCount} errors`);

    return new Response(
      JSON.stringify({
        success: true,
        message: `Processed ${processedCount} tasks in ${totalBatches} batches`,
        tasksProcessed: processedCount,
        tasksSuccessful: successCount,
        tasksWithErrors: errorCount,
        totalBatches: totalBatches,
        timeoutTasksReset: (timeoutTasks?.filter(t => t.retry_count < (t.max_retries || MAX_TASK_RETRIES)).length) || 0,
        timeoutTasksFailed: (timeoutTasks?.filter(t => t.retry_count >= (t.max_retries || MAX_TASK_RETRIES)).length) || 0,
        failedTasksRetried: retryTasks?.length || 0,
        runningTasksBefore: runningTaskCount,
        optimizedTimeoutMinutes: Math.floor(TASK_TIMEOUT / 60000),
        maxRetries: MAX_TASK_RETRIES
      }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );

  } catch (error) {
    console.error('Scraper Coordinator Error:', error);
    
    return new Response(
      JSON.stringify({
        success: false,
        message: 'Scraper coordinator failed',
        error: error.message,
        tasksProcessed: 0
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500
      }
    );
  } finally {
    // 10. 释放锁 - 删除锁记录
    try {
      await supabaseClient
        .from('scrape_tasks')
        .delete()
        .eq('batch_id', lockId)
        .eq('status', 'coordinator_lock');
    } catch (error) {
      console.error('Failed to release lock:', error);
    }
  }
});