import "jsr:@supabase/functions-js/edge-runtime.d.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

// 3. 缓存Access Token - 函数级别缓存
let cachedAccessToken: string | null = null;
let tokenExpiresAt: number = 0;

interface RedditPost {
  title: string;
  selftext: string;
  score: number;
  num_comments: number;
  subreddit: string;
  permalink: string;
  created_utc: number;
  author: string;
  url: string;
  id: string;
}

interface RedditResponse {
  data: {
    children: Array<{
      data: RedditPost;
    }>;
    after: string | null;
  };
}

interface FilterConfig {
  minScore?: number;           // 最小分数门槛 (默认: 20)
  minComments?: number;        // 最小评论数 (默认: 10)
  minTitleLength?: number;     // 最小标题长度 (默认: 5)
  maxTitleLength?: number;     // 最大标题长度 (默认: 无限制)
  pageLimit?: number;          // 每页帖子数量 (默认: 50)
  maxPages?: number;           // 最大页数 (默认: 按排序方法)
  requirePainPoints?: boolean; // 是否必须包含痛点 (默认: true)
  minPainScore?: number;       // 最小痛点分数门槛 (默认: 4)
  industryKeywords?: string[]; // 行业相关关键词
  excludeKeywords?: string[];  // 额外排除关键词
  dateRangeDays?: number;      // 日期范围天数 (默认: ±2天)
}

interface ScraperRequest {
  subreddits: string[];       // 要处理的subreddit列表
  target_date: string;        // YYYY-MM-DD format
  task_ids: number[];         // 对应的task ID用于状态更新
  batch_id: string;           // 批次ID
  filterConfig?: FilterConfig; // 可选筛选配置
}

interface ScraperResponse {
  success: boolean;
  message: string;
  totalProcessed: number;
  subredditsProcessed: number;
  taskResults: Array<{
    taskId: number;
    subreddit: string;
    processed: number;
    success: boolean;
    error?: string;
  }>;
}

// Rate limiting configuration
const REDDIT_API_RATE_LIMIT = 100; // 100 requests per minute
const RATE_LIMIT_WINDOW = 60 * 1000; // 1 minute in milliseconds
let requestCount = 0;
let windowStart = Date.now();

// Rate limiter function
async function rateLimitedRequest<T>(requestFn: () => Promise<T>): Promise<T> {
  const now = Date.now();

  // Reset window if needed
  if (now - windowStart >= RATE_LIMIT_WINDOW) {
    requestCount = 0;
    windowStart = now;
  }

  // Check if we've hit the rate limit
  if (requestCount >= REDDIT_API_RATE_LIMIT) {
    const waitTime = RATE_LIMIT_WINDOW - (now - windowStart);
    console.log(`⏳ Rate limit reached, waiting ${waitTime}ms...`);
    await new Promise(resolve => setTimeout(resolve, waitTime));

    // Reset after waiting
    requestCount = 0;
    windowStart = Date.now();
  }

  requestCount++;
  return await requestFn();
}

// Error classification for better handling
interface ClassifiedError {
  error: Error;
  isTemporary: boolean;
  isPermanent: boolean;
  shouldRetry: boolean;
  errorType: 'rate_limit' | 'not_found' | 'forbidden' | 'network' | 'unknown';
}

function classifyError(error: Error): ClassifiedError {
  const message = error.message.toLowerCase();

  // Rate limit timeout - special case where we hit rate limit but can't wait due to Edge Function timeout
  if (message.includes('rate limit timeout')) {
    return {
      error,
      isTemporary: true,
      isPermanent: false,
      shouldRetry: true,
      errorType: 'rate_limit'
    };
  }

  // Rate limiting - temporary, should retry
  if (message.includes('429') || message.includes('rate limit')) {
    return {
      error,
      isTemporary: true,
      isPermanent: false,
      shouldRetry: true,
      errorType: 'rate_limit'
    };
  }

  // Subreddit not found - permanent, should not retry
  if (message.includes('404') || message.includes('not found') ||
      message.includes('subreddit not found') || message.includes('banned') ||
      message.includes('private') || message.includes('quarantined')) {
    return {
      error,
      isTemporary: false,
      isPermanent: true,
      shouldRetry: false,
      errorType: 'not_found'
    };
  }

  // Forbidden access - likely permanent
  if (message.includes('403') || message.includes('forbidden') ||
      message.includes('access denied')) {
    return {
      error,
      isTemporary: false,
      isPermanent: true,
      shouldRetry: false,
      errorType: 'forbidden'
    };
  }

  // Network/timeout errors - temporary, should retry
  if (message.includes('timeout') || message.includes('network') ||
      message.includes('connection') || message.includes('econnreset')) {
    return {
      error,
      isTemporary: true,
      isPermanent: false,
      shouldRetry: true,
      errorType: 'network'
    };
  }

  // Unknown errors - assume temporary for now, but limit retries
  return {
    error,
    isTemporary: true,
    isPermanent: false,
    shouldRetry: true,
    errorType: 'unknown'
  };
}

// Enhanced retry mechanism with timeout awareness for Edge Functions
async function retryWithBackoff<T>(
  fn: () => Promise<T>,
  maxRetries: number = 3,
  baseDelay: number = 1000,
  maxExecutionTime: number = 240000 // 4 minutes max execution time for Edge Functions
): Promise<T> {
  let lastClassifiedError: ClassifiedError;
  const startTime = Date.now();

  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      return await fn();
    } catch (error) {
      const classifiedError = classifyError(error as Error);
      lastClassifiedError = classifiedError;

      console.log(`⚠️ Attempt ${attempt + 1} failed: ${error.message}`);
      console.log(`🔍 Error classification: ${classifiedError.errorType} (temporary: ${classifiedError.isTemporary}, should retry: ${classifiedError.shouldRetry})`);

      // Don't retry permanent errors
      if (classifiedError.isPermanent || !classifiedError.shouldRetry) {
        console.log(`🛑 Permanent error detected, stopping retries: ${classifiedError.errorType}`);
        throw classifiedError.error;
      }

      if (attempt === maxRetries) {
        throw classifiedError.error;
      }

      // Calculate delay based on error type
      let delay: number;
      switch (classifiedError.errorType) {
        case 'rate_limit':
          delay = 60000; // 1 minute for rate limits
          break;
        case 'network':
          delay = baseDelay * Math.pow(2, attempt); // Exponential backoff for network issues
          break;
        default:
          delay = baseDelay * Math.pow(1.5, attempt); // Slower backoff for unknown errors
      }

      // Check if waiting would exceed Edge Function timeout
      const elapsedTime = Date.now() - startTime;
      const timeAfterDelay = elapsedTime + delay;

      if (timeAfterDelay > maxExecutionTime) {
        console.log(`⏰ Cannot wait ${delay}ms - would exceed Edge Function timeout (${elapsedTime}ms elapsed, ${maxExecutionTime}ms max)`);
        console.log(`🔄 Throwing rate limit error to mark task as failed for later retry`);

        // For rate limit errors that would cause timeout, throw a special error
        // that indicates this should be retried later
        if (classifiedError.errorType === 'rate_limit') {
          throw new Error(`Rate limit timeout: Cannot wait ${delay}ms without exceeding Edge Function timeout`);
        } else {
          throw classifiedError.error;
        }
      }

      console.log(`⏳ Retrying in ${delay}ms... (${maxRetries - attempt} attempts remaining)`);
      console.log(`⏰ Time elapsed: ${elapsedTime}ms, estimated time after delay: ${timeAfterDelay}ms`);
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }

  throw lastClassifiedError!.error;
}

// 6. 移除无效subreddit - 用户要求最大化post数量，所以不过滤任何subreddit
const PROBLEMATIC_SUBREDDITS = new Set<string>([
  // 用户要求最大化数据，所以不过滤任何subreddit
]);

// Industry mapping based on updated 39 categories
const INDUSTRY_MAPPING = {
  'SaaS & Cloud Services': {
    id: 1,
    subreddits: ['SaaS', 'cloud', 'aws', 'azure', 'googlecloud', 'SaaSMarketing', 'microsaas', 'SaaSSales'],
    keywords: ['saas', 'software as a service', 'cloud', 'platform', 'subscription', 'api', 'service', 'kubernetes', 'docker', 'serverless', 'microservices']
  },

  'Mobile App Development': {
    id: 4,
    subreddits: ['androiddev', 'iOSProgramming', 'flutter', 'UIUX', 'FlutterDev', 'reactnative', 'ionic'],
    keywords: ['mobile', 'app', 'android', 'ios', 'flutter', 'react native', 'swift', 'kotlin', 'cross platform', 'mobile ui', 'app store']
  },
  'Web & Frontend Development': {
    id: 5,
    subreddits: ['webdev', 'javascript', 'reactjs', 'webassembly', 'Frontend', 'web_design', 'learnjavascript'],
    keywords: ['web', 'frontend', 'javascript', 'react', 'vue', 'angular', 'css', 'html', 'typescript', 'responsive', 'performance', 'ui/ux']
  },
  'No-Code/Low-Code Platforms': {
    id: 6,
    subreddits: ['NoCode', 'LowCode', 'Bubble', 'Makerpad', 'nocode', 'Airtable', 'zapier', 'webflow', 'NoCodeSaaS', 'lowcode'],
    keywords: ['nocode', 'no code', 'low code', 'automation', 'workflow', 'integration', 'zapier', 'bubble', 'webflow', 'airtable', 'citizen developer']
  },

  'AI & Machine Learning': {
    id: 8,
    subreddits: ['MachineLearning', 'datascience', 'OpenAI', 'LLM', 'LanguageTechnology', 'DeepLearning', 'NeuralNetworks', 'ArtificialIntelligence', 'AI', 'ChatGPT', 'ArtificialInteligence', 'artificial', 'aiArt', 'CharGPTPromptGenius', 'LocalLLaMA', 'ChatGPTPro', 'ChatGPTCoding', 'ClaudeAI', 'aivideo', 'AI_Agents', 'automation', 'aipromptprogramming', 'Bard', 'perplexity_ai', 'GeminiAI', 'cursor', 'GoogleGeminiAI', 'DeepSeek', 'midjourney', 'StableDiffusion', 'PromptEngineering', 'ollama', 'LocalLLM', 'OpenSourceAi'],
    keywords: ['ai', 'artificial intelligence', 'machine learning', 'deep learning', 'neural network', 'llm', 'nlp', 'computer vision', 'data science', 'mlops']
  },
  'E-commerce & Retail': {
    id: 9,
    subreddits: ['ecommerce', 'Shopify', 'AmazonSeller', 'AmazonFBA', 'dropship', 'Etsy', 'EtsySellers', 'reviewmyshopify', 'ecommercemarketing'],
    keywords: ['ecommerce', 'e-commerce', 'retail', 'shop', 'marketplace', 'online store', 'dropshipping', 'amazon', 'shopify', 'payment']
  },
  'Health & Fitness Tech': {
    id: 10,
    subreddits: ['fitness', 'DigitalHealth', 'WearOS', 'healthtech', 'MedTech', 'QuantifiedSelf', 'sleephackers', 'Biohackers', 'healthIT', 'Fitness', 'strength_training', 'loseit', 'Health', 'GYM', 'GymMotivation', 'workout', 'fitness30plus', 'physicaltherapy', 'personaltraining', 'WeightTraining'],
    keywords: ['health', 'healthcare', 'medical', 'fitness', 'wellness', 'telemedicine', 'nutrition', 'mental health', 'wearable', 'health tech']
  },
  'EdTech': {
    id: 11,
    subreddits: ['edtech', 'learnprogramming', 'OnlineTutoring', 'education', 'instructionaldesign', 'Elearning', 'teachers'],
    keywords: ['education', 'edtech', 'learning', 'teaching', 'course', 'training', 'skill', 'knowledge', 'school', 'university', 'lms', 'e-learning']
  },
  'FinTech': {
    id: 12,
    subreddits: ['fintech', 'InsurTech'],
    keywords: ['fintech', 'finance', 'payment', 'banking', 'financial services', 'digital banking', 'insurtech', 'regtech']
  },
  'Startup & Business': {
    id: 13,
    subreddits: ['Entrepreneur', 'business', 'startups', 'EntrepreneurRideAlong', 'SideProject', 'startup', 'ycombinator', 'Entrepreneurship', 'indiehackers', 'Entrepreneurs', 'growmybusiness', 'indiebiz', 'thesidehustle'],
    keywords: ['startup', 'business', 'entrepreneur', 'venture', 'funding', 'investment', 'business model', 'innovation', 'growth', 'scaling']
  },
  'Consumer Services & Freelance': {
    id: 14,
    subreddits: ['SideHustle', 'smallbusiness', 'freelance', 'BeerMoney', 'DigitalNomad', 'Fiverr', 'WorkOnline', 'forhire', 'freelanceWriters', 'freelance_forhire', 'Upwork', 'Freelancers', 'remotework', 'RemoteJobHunters'],
    keywords: ['service', 'consumer', 'local', 'home', 'food', 'delivery', 'cleaning', 'repair', 'maintenance', 'gig economy', 'freelance']
  },
  'Enterprise & B2B Services': {
    id: 15,
    subreddits: ['b2b', 'CRM', 'Procurement'],
    keywords: ['b2b', 'enterprise', 'business', 'crm', 'erp', 'workflow', 'collaboration', 'hr', 'sales', 'marketing', 'project management']
  },
  'Digital Marketing & SEO': {
    id: 16,
    subreddits: ['SEO', 'DigitalMarketing', 'digital_marketing', 'Affiliatemarketing', 'PPC', 'advertising', 'FacebookAds', 'content_marketing', 'bigseo', 'Emailmarketing', 'AskMarketing', 'googleads', 'MarketingResearch', 'GrowthHacking', 'DigitalMarketingHack', 'TechSEO', 'seogrowth', 'marketing'],
    keywords: ['seo', 'digital marketing', 'content marketing', 'ppc', 'advertising', 'email marketing', 'social media marketing', 'affiliate marketing', 'growth hacking']
  },
  'Social Media Marketing & Influencers': {
    id: 17,
    subreddits: ['socialmedia', 'discord', 'communitymanagement', 'SocialMediaMarketing', 'BeautyFuruChatter', 'Instagram', 'InstagramMarketing', 'inflencermarketing', 'InstagramGrowthTips'],
    keywords: ['social media', 'influencer', 'instagram', 'tiktok', 'youtube', 'community management', 'social marketing', 'engagement', 'followers']
  },
  'Media & Content Creation': {
    id: 18,
    subreddits: ['youtubers', 'podcasting', 'CreatorEconomy', 'vlogging', 'NewTubers', 'ContentCreators', 'blogging', 'VideoEditing', 'bideography', 'premiere', 'editors', 'finalcutpro', 'BideoEditors', 'Youtubevideo'],
    keywords: ['content', 'media', 'video', 'audio', 'podcast', 'blog', 'design', 'editing', 'streaming', 'creator', 'youtube', 'video editing']
  },
  'Photography & Visual Arts': {
    id: 19,
    subreddits: ['photography', 'analog', 'AskPhotography', 'streetphotography', 'postprocessing', 'AnalogCommunity', 'WeddingPhotography', 'Beginning_Photography'],
    keywords: ['photography', 'photo', 'camera', 'visual arts', 'image', 'picture', 'portrait', 'landscape', 'digital photography', 'film photography']
  },
  'Design & Creative Tools': {
    id: 20,
    subreddits: ['design', 'graphic_design', 'web_design', 'UI_Design', 'Adobe', 'Figma', 'creativity', 'typography', 'logodesign', 'UXDesign', 'userexperience', 'UXResearch', 'learndesign', 'product_design', 'UX_Design', 'FigmaDesign'],
    keywords: ['design', 'creative', 'graphic design', 'ui/ux', 'adobe', 'figma', 'photoshop', 'illustration', 'branding', 'visual design']
  },
  'Travel & Transportation': {
    id: 21,
    subreddits: ['travel', 'solotravel', 'airbnb', 'wanderlust', 'shoestring', 'travelhacks', 'backpacking', 'DigitalNomad'],
    keywords: ['travel', 'trip', 'vacation', 'hotel', 'flight', 'transportation', 'booking', 'tourism', 'nomad', 'journey']
  },
  'GreenTech & Sustainability': {
    id: 22,
    subreddits: ['sustainability', 'renewable', 'cleantech', 'RenewableEnergy', 'Envirotech', 'solar'],
    keywords: ['sustainability', 'green', 'eco', 'environment', 'renewable', 'climate', 'carbon', 'energy', 'waste', 'recycling', 'clean tech']
  },
  'Logistics & Supply Chain': {
    id: 23,
    subreddits: ['logistics', 'warehouse', 'operations', 'supplychain', 'inventory'],
    keywords: ['logistics', 'supply chain', 'shipping', 'warehouse', 'inventory', 'freight', 'delivery', 'procurement', 'operations', 'manufacturing']
  },
  'Gaming & Entertainment': {
    id: 24,
    subreddits: ['gaming', 'gamedev', 'VirtualReality', 'GamingIndustry', 'eSports', 'VRGaming', 'boardgames'],
    keywords: ['gaming', 'game', 'entertainment', 'streaming', 'content', 'video game', 'mobile game', 'vr', 'ar', 'unity', 'unreal']
  },

  'AR/VR & Metaverse': {
    id: 26,
    subreddits: ['virtualreality', 'oculus', 'augmentedreality', 'Metaverse'],
    keywords: ['ar', 'vr', 'augmented reality', 'virtual reality', 'metaverse', 'oculus', 'quest', 'immersive', '3d', 'spatial computing']
  },
  'BioTech & MedTech': {
    id: 27,
    subreddits: ['biotech', 'biotechnology', 'bioinformatics', 'genomics', 'labrats'],
    keywords: ['biotech', 'biotechnology', 'medical technology', 'genomics', 'bioinformatics', 'pharmaceuticals', 'lab', 'research', 'clinical', 'diagnosis']
  },
  'LegalTech': {
    id: 28,
    subreddits: ['legaltech', 'law', 'legaladvice'],
    keywords: ['legal tech', 'law', 'legal', 'compliance', 'contract', 'attorney', 'lawyer', 'paralegal', 'court', 'litigation']
  },
  'PropTech': {
    id: 29,
    subreddits: ['PropTech', 'RealEstate', 'SmartHome'],
    keywords: ['proptech', 'real estate', 'property', 'rental', 'smart home', 'mortgage', 'real estate investment', 'home automation', 'construction', 'architecture']
  },
  'Data Science & Analytics': {
    id: 30,
    subreddits: ['datascience', 'analytics', 'statistics', 'tableau', 'PowerBI', 'bigdata', 'dataengineering', 'BusinessIntelligence', 'dataanalysis', 'dataanalytics'],
    keywords: ['data science', 'analytics', 'business intelligence', 'big data', 'statistics', 'visualization', 'dashboard', 'reporting', 'insights', 'data mining']
  },
  'Blockchain & Cryptocurrency': {
    id: 31,
    subreddits: ['CryptoCurrency', 'blockchain', 'ethereum', 'Bitcoin', 'DeFi', 'NFT', 'Web3', 'ethtrader', 'CryptoMarkets', 'solana', 'BitcoinBeginners', 'defi', 'web3'],
    keywords: ['blockchain', 'cryptocurrency', 'bitcoin', 'ethereum', 'defi', 'nft', 'web3', 'smart contracts', 'crypto trading', 'digital assets']
  },
  'Stock Investment & Trading': {
    id: 32,
    subreddits: ['stocks', 'Daytrading', 'SotckMarket', 'investing', 'finance', 'options', 'dividends', 'ValueInvesting', 'Trading', 'swingtrading', 'StocksAndTrading'],
    keywords: ['stocks', 'trading', 'investment', 'finance', 'stock market', 'portfolio', 'dividend', 'options', 'forex', 'commodities']
  },
  'Financial Independence & Personal Finance': {
    id: 33,
    subreddits: ['TheRaceTo10Million', 'Fire', 'fatFIRE', 'leanfire', 'personalfinance', 'Frugal', 'financialindependence', 'FinancialPlanning', 'UKPersonalFinance', 'PersonalFinanceCanada', 'lifehacks', 'productivity', 'getdisciplined', 'lifehack'],
    keywords: ['financial independence', 'fire', 'personal finance', 'retirement', 'saving', 'budgeting', 'frugal', 'money management', 'productivity', 'life optimization']
  },
  'Audio & Podcast': {
    id: 34,
    subreddits: ['podcasting', 'podcasts', 'audio', 'spotify', 'audioengineering', 'voiceover', 'audiobooks'],
    keywords: ['podcast', 'audio', 'music', 'sound', 'radio', 'voice', 'audiobook', 'streaming', 'recording', 'editing']
  },
  'AgTech': {
    id: 35,
    subreddits: ['agriculture', 'farming', 'AgTech', 'permaculture', 'gardening'],
    keywords: ['agriculture', 'farming', 'agtech', 'food production', 'sustainable farming', 'precision agriculture', 'vertical farming', 'greenhouse', 'crop monitoring', 'livestock']
  },
  'Pet Care & Community': {
    id: 36,
    subreddits: ['cats', 'dogs', 'Dogtraining', 'Aquariums', 'dogswithjobs', 'RATS', 'BeardedDragons', 'birding', 'DOG', 'DogAdvice', 'cat', 'Ornithology', 'Pets', 'germanshepherds', 'reptiles', 'herpetology', 'ballpython', 'leopardgeckos', 'turtle', 'PetAdvice', 'DogTrainingTips', 'Dogowners', 'dogbreed', 'DogBreeds101', 'cockatiel', 'doggrooming', 'CatAdvice', 'puppy101'],
    keywords: ['pets', 'animals', 'dogs', 'cats', 'pet care', 'veterinary', 'animal health', 'pet products', 'pet training', 'pet community']
  },
  'Family & Parenting': {
    id: 37,
    subreddits: ['Parenting', 'daddit', 'SingleParents', 'beyondthebump', 'toddlers', 'NewParents', 'raisingkids', 'parentsofmultiples', 'Parents'],
    keywords: ['parenting', 'family', 'children', 'kids', 'baby', 'child development', 'education', 'family life', 'pregnancy', 'childcare']
  },
  'General/Trending Topics': {
    id: 38,
    subreddits: [
      'AskReddit', 'IAMA', 'funny', 'worldnews', 'todayilearned',
      'aww', 'Music', 'movies', 'memes', 'Showerthoughts', 'science',
      'pics', 'Jokes', 'news', 'explainlikeimfive', 'books', 'food',
      'LifeProTips', 'DIY', 'GetMotivated', 'askscience'
    ],
    keywords: [
      'general', 'popular', 'trending', 'viral', 'community', 'discussion',
      'entertainment', 'humor', 'advice', 'life', 'culture', 'current events',
      'learning', 'tips', 'motivation', 'science', 'lifestyle', 'social'
    ]
  },
  'MRR showcase': {
    id: 39,
    subreddits: [
      // 核心独立开发/收入类
      'indiehackers', 'SaaS', 'SideProject', 'EntrepreneurRideAlong', 'Entrepreneur',
      'startups', 'SmallBusiness', 'solopreneur', 'bootstrapped', 'Passive_Income',
      'microentrepreneur', 'financialindependence',
      // 行业/技术侧重相关
      'gamedev', 'webdev', 'learnprogramming', 'Python', 'learnpython',
      'ProgrammingBuddies', 'developers', 'fullstack', 'coding', 'django',
      'reactjs', 'javascript',
      // 创收/赚钱类
      'sidehustle', 'make_money_online', 'WorkOnline', 'digital_marketing',
      'freelance', 'RemoteWork', 'Upwork', 'shopify',
      // 其他可能涉及收入分享主题
      'passiveincome', 'investing', 'business', 'BizOps', 'SoloFounders',
      'MicroSaaS', 'FemaleFounders'
    ],
    keywords: [
      // 核心MRR关键词
      'mrr', 'arr', 'monthly recurring revenue', 'monthly revenue', 'monthly income',
      'revenue report', 'income report', 'earnings report', 'indiehacker income',
      'show your mrr', 'show your revenue', 'monthly update', 'income update',
      'my saas income', 'my saas revenue', 'side project revenue', 'side project income',
      'side project mrr', 'earnings milestone', 'indie dev earnings', 'business update',
      'bootstrapped revenue', 'bootstrapped income',
      // 收入展示相关
      'made $', 'just hit $', 'milestone $', 'hit $ in revenue', 'how much did you make',
      'how much i made', 'personal finance goal',
      // 验证和成功指标
      'validated', 'market validation', 'paying customers', 'first sale', 'revenue stream',
      'profitable', 'break even', 'cash flow positive', 'sustainable business'
    ]
  }
};

// Get subreddits for specific industries - 移除限制
function getSubredditsForIndustries(industryIds: number[]): string[] {
  const subreddits = new Set<string>();

  industryIds.forEach(industryId => {
    const industry = Object.values(INDUSTRY_MAPPING).find(ind => ind.id === industryId);
    if (industry) {
      // 使用所有subreddit，不进行任何过滤和限制
      industry.subreddits.forEach(sub => subreddits.add(sub));
    }
  });

  return Array.from(subreddits);
}

// Map subreddit to industry ID
function classifySubredditToIndustry(subreddit: string): number {
  const sub = subreddit.toLowerCase();

  for (const [industryName, config] of Object.entries(INDUSTRY_MAPPING)) {
    if (config.subreddits.some(s => s.toLowerCase() === sub)) {
      return config.id;
    }
  }

  // Default to General/Trending Topics if not found
  return 38;
}

// 3. Reddit API authentication with caching
async function getRedditAccessToken(): Promise<string> {
  const now = Date.now();
  
  // 检查缓存的token是否仍有效
  if (cachedAccessToken && now < tokenExpiresAt) {
    console.log('🔄 Using cached Reddit access token');
    return cachedAccessToken;
  }

  console.log('🔑 Fetching new Reddit access token');
  const clientId = Deno.env.get('REDDIT_CLIENT_ID');
  const clientSecret = Deno.env.get('REDDIT_CLIENT_SECRET');
  const userAgent = Deno.env.get('REDDIT_USER_AGENT') || 'IdeaHunter/1.0';

  if (!clientId || !clientSecret) {
    throw new Error('Reddit API credentials not configured');
  }

  const auth = btoa(`${clientId}:${clientSecret}`);
  
  const response = await fetch('https://www.reddit.com/api/v1/access_token', {
    method: 'POST',
    headers: {
      'Authorization': `Basic ${auth}`,
      'User-Agent': userAgent,
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    body: 'grant_type=client_credentials'
  });

  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(`Reddit auth failed: ${response.status} - ${errorText}`);
  }

  const data = await response.json();
  
  // 缓存token，设置55分钟过期（Reddit token通常1小时过期）
  cachedAccessToken = data.access_token;
  tokenExpiresAt = now + (55 * 60 * 1000);
  
  return cachedAccessToken!;
}

// 日志记录接口
interface SortMethodLog {
  method: string;
  timeParam: string | null;
  fetched: number;
  dateFiltered: number;
  qualityFiltered: number;
  final: number;
}

interface SubredditLog {
  subreddit: string;
  startTime: string;
  sortMethods: SortMethodLog[];
  totalFetched: number;
  totalDateFiltered: number;
  totalQualityFiltered: number;
  totalFinal: number;
  duplicatesRemoved: number;
  endTime: string;
  duration: number;
}

// 日志格式化工具函数
function formatSubredditHeader(subreddit: string): string {
  const line = '='.repeat(80);
  const title = `📊 SUBREDDIT: r/${subreddit}`;
  const padding = Math.max(0, (80 - title.length) / 2);
  return `\n${line}\n${' '.repeat(Math.floor(padding))}${title}\n${line}`;
}

function formatSortMethodTable(sortMethods: SortMethodLog[]): string {
  const header = '| 排序方法 | 时间参数 | 原始获取 | 日期过滤 | 质量过滤 | 最终数量 |';
  const separator = '|----------|----------|----------|----------|----------|----------|';

  const rows = sortMethods.map(sm => {
    const method = sm.method.padEnd(8);
    const timeParam = (sm.timeParam || 'N/A').padEnd(8);
    const fetched = sm.fetched.toString().padStart(8);
    const dateFiltered = sm.dateFiltered.toString().padStart(8);
    const qualityFiltered = sm.qualityFiltered.toString().padStart(8);
    const final = sm.final.toString().padStart(8);
    return `| ${method} | ${timeParam} | ${fetched} | ${dateFiltered} | ${qualityFiltered} | ${final} |`;
  });

  return [header, separator, ...rows].join('\n');
}

function formatSubredditSummary(log: SubredditLog): string {
  const line = '-'.repeat(80);
  return `
${line}
📈 r/${log.subreddit} 抓取汇总报告
${line}
⏰ 开始时间: ${new Date(log.startTime).toLocaleString()}
⏰ 结束时间: ${new Date(log.endTime).toLocaleString()}
⏱️  总耗时: ${(log.duration / 1000).toFixed(2)} 秒

📊 数据流转统计:
   🔍 原始获取: ${log.totalFetched} 个帖子
   📅 日期过滤: ${log.totalDateFiltered} 个帖子 (过滤掉 ${log.totalFetched - log.totalDateFiltered} 个)
   ✅ 质量过滤: ${log.totalQualityFiltered} 个帖子 (过滤掉 ${log.totalDateFiltered - log.totalQualityFiltered} 个)
   🔄 去重处理: ${log.totalFinal} 个帖子 (去重 ${log.duplicatesRemoved} 个)

📋 各排序方法详情:
${formatSortMethodTable(log.sortMethods)}
${line}`;
}

// 1. Fetch posts from a subreddit with detailed logging - 大幅优化
async function fetchRedditPosts(subreddit: string, accessToken: string, targetDate: string, filterConfig?: FilterConfig): Promise<RedditPost[]> {
  const userAgent = Deno.env.get('REDDIT_USER_AGENT') || 'IdeaHunter/1.0';
  const maxRetries = 2; // 减少重试次数

  // 应用筛选配置，设置默认值
  const config = {
    minScore: filterConfig?.minScore ?? 20,           // 默认最小分数20
    minComments: filterConfig?.minComments ?? 5,     // 默认最小评论数10
    minTitleLength: filterConfig?.minTitleLength ?? 5, // 默认最小标题长度5
    maxTitleLength: filterConfig?.maxTitleLength ?? Infinity,
    pageLimit: filterConfig?.pageLimit ?? 20,
    requirePainPoints: filterConfig?.requirePainPoints ?? true, // 默认必须包含痛点
    minPainScore: filterConfig?.minPainScore ?? 4,    // 默认最小痛点分数4
    industryKeywords: filterConfig?.industryKeywords ?? [],
    excludeKeywords: filterConfig?.excludeKeywords ?? [],
    dateRangeDays: filterConfig?.dateRangeDays ?? 2
  };

  // 创建日志记录对象
  const subredditLog: SubredditLog = {
    subreddit,
    startTime: new Date().toISOString(),
    sortMethods: [],
    totalFetched: 0,
    totalDateFiltered: 0,
    totalQualityFiltered: 0,
    totalFinal: 0,
    duplicatesRemoved: 0,
    endTime: '',
    duration: 0
  };

  console.log(formatSubredditHeader(subreddit));
  console.log(`🚀 开始抓取 r/${subreddit} - ${new Date().toLocaleString()}`);
  console.log(`📋 筛选配置: 分数>=${config.minScore}, 评论>=${config.minComments}, 标题长度>=${config.minTitleLength}, 必须痛点=${config.requirePainPoints}, 最小痛点分数>=${config.minPainScore}, 日期范围±${config.dateRangeDays}天`);

  // Convert target date to Unix timestamps for filtering - 使用配置的日期范围
  const targetDateObj = new Date(targetDate);
  // 使用配置的日期范围
  const startOfRange = new Date(targetDateObj.getFullYear(), targetDateObj.getMonth(), targetDateObj.getDate() - config.dateRangeDays, 0, 0, 0);
  const endOfRange = new Date(targetDateObj.getFullYear(), targetDateObj.getMonth(), targetDateObj.getDate() + config.dateRangeDays, 23, 59, 59);
  const startTimestamp = Math.floor(startOfRange.getTime() / 1000);
  const endTimestamp = Math.floor(endOfRange.getTime() / 1000);

  const startDateStr = startOfRange.toISOString().split('T')[0];
  const endDateStr = endOfRange.toISOString().split('T')[0];
  console.log(`📅 目标日期范围: ${startDateStr} 到 ${endDateStr} (${Math.ceil((endOfRange.getTime() - startOfRange.getTime()) / (1000 * 60 * 60 * 24))} 天)`);

  const allPosts: RedditPost[] = [];

  // 使用所有Reddit API支持的排序方法以最大化数据抓取
  const sortMethods = [
    { method: 'hot', timeParam: null, maxPages: 2 },      // 热门帖子
    { method: 'top', timeParam: 'day', maxPages: 3 },     // 当日最高评分
    { method: 'top', timeParam: 'week', maxPages: 2 },    // 本周最高评分
    { method: 'top', timeParam: 'month', maxPages: 2 },   // 本月最高评分
    { method: 'new', timeParam: null, maxPages: 4 },      // 最新帖子（最可能有目标日期的内容）
    { method: 'rising', timeParam: null, maxPages: 2 },   // 上升趋势帖子
    { method: 'controversial', timeParam: 'day', maxPages: 2 },  // 当日争议性帖子
    { method: 'controversial', timeParam: 'week', maxPages: 1 }  // 本周争议性帖子
  ];
  
  for (const sortConfig of sortMethods) {
    const { method: sortMethod, timeParam, maxPages } = sortConfig;
    let after: string | null = null;
    let totalFetched = 0;
    let emptyPages = 0; // 空页面计数
    const maxEmptyPages = 1; // 最多1个空页面就停止

    // 为当前排序方法创建日志记录
    const sortMethodLog: SortMethodLog = {
      method: sortMethod,
      timeParam: timeParam,
      fetched: 0,
      dateFiltered: 0,
      qualityFiltered: 0,
      final: 0
    };

    const sortMethodName = timeParam ? `${sortMethod}(${timeParam})` : sortMethod;
    console.log(`\n🔍 开始抓取 ${sortMethodName} 排序的帖子...`);

    for (let page = 0; page < maxPages; page++) {
      let retries = 0;
      while (retries < maxRetries) {
        try {
          const params = new URLSearchParams({
            limit: config.pageLimit.toString(), // 使用配置的页面限制
            raw_json: '1'
          });
          
          if (timeParam) {
            params.set('t', timeParam);
          }
          
          if (after) {
            params.set('after', after);
          }

          const url = `https://oauth.reddit.com/r/${subreddit}/${sortMethod}?${params}`;
          console.log(`🔗 Fetching: ${url}`);
          
          const response = await fetch(url, {
            headers: {
              'Authorization': `Bearer ${accessToken}`,
              'User-Agent': userAgent,
            },
          });

          if (response.status === 429) {
            const retryAfter = Math.min(parseInt(response.headers.get('retry-after') || '60', 10), 120); // 增加等待时间
            console.log(`⏱️ Rate limited for r/${subreddit}, waiting ${retryAfter}s... (attempt ${retries + 1}/${maxRetries})`);
            await new Promise(resolve => setTimeout(resolve, retryAfter * 1000));
            retries++;
            continue;
          }

          // Enhanced error handling for different HTTP status codes
          if (!response.ok) {
            let errorMessage = `HTTP ${response.status}: ${response.statusText}`;

            switch (response.status) {
              case 404:
                errorMessage = `Subreddit r/${subreddit} not found or does not exist`;
                break;
              case 403:
                errorMessage = `Access forbidden to r/${subreddit} - may be private, banned, or quarantined`;
                break;
              case 401:
                errorMessage = `Unauthorized access to r/${subreddit} - authentication issue`;
                break;
              case 500:
              case 502:
              case 503:
              case 504:
                errorMessage = `Reddit server error (${response.status}) for r/${subreddit} - temporary issue`;
                break;
              default:
                errorMessage = `HTTP ${response.status}: ${response.statusText} for r/${subreddit}`;
            }

            throw new Error(errorMessage);
          }

          const data: RedditResponse = await response.json();
          
          if (!data.data || !Array.isArray(data.data.children)) {
            break;
          }

          const posts = data.data.children.map(child => child.data);

          // 更新排序方法日志 - 原始获取数量
          sortMethodLog.fetched += posts.length;
          console.log(`   📥 本页获取: ${posts.length} 个帖子`);

          // 提前过滤 - 预过滤低质量帖子，使用配置的筛选条件
          const preFiltedPosts = posts.filter(post => {
            if (!post.title || post.title === '[deleted]' || post.title === '[removed]') return false;
            if (!post.author || post.author === '[deleted]') return false;
            if (post.score < config.minScore || post.num_comments < config.minComments) return false;
            return true;
          });

          console.log(`   🔍 质量预过滤: ${posts.length} -> ${preFiltedPosts.length} (过滤掉 ${posts.length - preFiltedPosts.length} 个)`);

          // Filter posts by target date
          const datePosts = preFiltedPosts.filter(post => {
            const postTimestamp = post.created_utc;
            const isInRange = postTimestamp >= startTimestamp && postTimestamp <= endTimestamp;
            return isInRange;
          });

          // 更新排序方法日志 - 日期过滤数量
          sortMethodLog.dateFiltered += datePosts.length;
          console.log(`   📅 日期过滤: ${preFiltedPosts.length} -> ${datePosts.length} (过滤掉 ${preFiltedPosts.length - datePosts.length} 个)`);

          // 进一步的内容质量检查，传递筛选配置
          const validPosts = datePosts.filter(post => isValidPost(post, config));

          // 更新排序方法日志 - 质量过滤数量
          sortMethodLog.qualityFiltered += validPosts.length;
          console.log(`   📝 质量过滤: ${datePosts.length} -> ${validPosts.length} (过滤掉 ${datePosts.length - validPosts.length} 个)`);

          console.log(`   📊 ${sortMethodName} 本页流程: ${posts.length} -> ${preFiltedPosts.length} -> ${datePosts.length} -> ${validPosts.length}`);
          
          if (datePosts.length === 0) {
            emptyPages++;
            if (emptyPages >= maxEmptyPages) {
              console.log(`🛑 Stopping fetch for r/${subreddit} due to empty pages`);
              break;
            }
          } else {
            emptyPages = 0; // 重置空页面计数
          }
          
          // 更激进的质量检查 - 如果这页质量太低就停止
          if (datePosts.length > 0 && validPosts.length / datePosts.length < 0.2) {
            console.log(`⚠️ Low quality ratio for r/${subreddit}, stopping early`);
            break;
          }
          
          allPosts.push(...datePosts);
          totalFetched += datePosts.length;
          
          after = data.data.after;
          
          if (!after || datePosts.length === 0) {
            break;
          }

          await new Promise(resolve => setTimeout(resolve, 1000)); // 增加延迟到1秒
          break;
          
        } catch (error) {
          console.error(`Error fetching r/${subreddit} ${sortMethod} page ${page}, retry ${retries + 1}:`, error);
          retries++;
          
          if (retries >= maxRetries) {
            console.error(`Max retries reached for r/${subreddit} ${sortMethod}, skipping`);
            break;
          }
          
          await new Promise(resolve => setTimeout(resolve, 1000 * retries)); // 减少重试延迟
        }
      }
      
      if (retries >= maxRetries || emptyPages >= maxEmptyPages) break;
    }

    // 更新排序方法日志的最终数量
    sortMethodLog.final = totalFetched;
    subredditLog.sortMethods.push(sortMethodLog);

    console.log(`✅ ${sortMethodName} 完成: 获取 ${sortMethodLog.fetched} -> 日期过滤 ${sortMethodLog.dateFiltered} -> 质量过滤 ${sortMethodLog.qualityFiltered} -> 最终 ${sortMethodLog.final} 个帖子`);
  }

  // 更新总体统计
  subredditLog.totalFetched = subredditLog.sortMethods.reduce((sum, sm) => sum + sm.fetched, 0);
  subredditLog.totalDateFiltered = subredditLog.sortMethods.reduce((sum, sm) => sum + sm.dateFiltered, 0);
  subredditLog.totalQualityFiltered = subredditLog.sortMethods.reduce((sum, sm) => sum + sm.qualityFiltered, 0);
  subredditLog.totalFinal = allPosts.length;

  // 2. 预处理去重 - 在返回前去重
  const seenIds = new Set<string>();
  const uniquePosts = allPosts.filter(post => {
    if (seenIds.has(post.id)) {
      return false;
    }
    seenIds.add(post.id);
    return true;
  });

  // 更新去重统计
  subredditLog.duplicatesRemoved = allPosts.length - uniquePosts.length;
  subredditLog.totalFinal = uniquePosts.length;
  subredditLog.endTime = new Date().toISOString();
  subredditLog.duration = new Date(subredditLog.endTime).getTime() - new Date(subredditLog.startTime).getTime();

  // 输出详细的汇总报告
  console.log(formatSubredditSummary(subredditLog));

  return uniquePosts;
}

// 痛点关键词识别系统 - 用于识别用户痛苦和需求
const PAIN_POINT_KEYWORDS = {
  // 高权重痛点词汇 (权重: 3)
  high_impact: [
    'hate', 'sucks', 'terrible', 'awful', 'horrible', 'nightmare', 'disaster',
    'broken', 'useless', 'garbage', 'trash', 'worst', 'fed up', 'sick of',
    'can\'t stand', 'driving me crazy', 'makes me mad', 'infuriating'
  ],

  // 中权重痛点词汇 (权重: 2)
  medium_impact: [
    'frustrating', 'annoying', 'painful', 'difficult', 'hard', 'struggle',
    'problem', 'issue', 'trouble', 'pain', 'headache', 'stress', 'burden',
    'inefficient', 'slow', 'waste', 'takes forever', 'time consuming',
    'complicated', 'confusing', 'overwhelming', 'exhausting'
  ],

  // 寻求替代方案 (权重: 2.5)
  seeking_alternatives: [
    'alternative', 'better way', 'replacement', 'instead of', 'switch from',
    'migrate from', 'move away from', 'looking for', 'need something',
    'anything better', 'other options', 'different approach', 'new solution'
  ],

  // 缺失功能表达 (权重: 2)
  missing_features: [
    'missing', 'lack', 'need', 'wish', 'want', 'should have', 'would be nice',
    'if only', 'why doesn\'t', 'why can\'t', 'no way to', 'impossible to',
    'can\'t find', 'doesn\'t exist', 'no solution', 'gap in'
  ],

  // 推广现有产品相关 (权重: 2.5) - 新增类别
  product_promotion: [
    'check out', 'try this', 'recommend', 'suggestion', 'built this',
    'created this', 'made this', 'my product', 'our product', 'my app',
    'our app', 'my tool', 'our tool', 'my service', 'our service',
    'launching', 'just released', 'new feature', 'feedback wanted',
    'thoughts on', 'what do you think', 'would you use', 'beta test',
    'early access', 'free trial', 'discount code', 'promo code',
    'startup', 'side project', 'passion project', 'indie maker',
    'bootstrapped', 'mvp', 'minimum viable product', 'product hunt',
    'show hn', 'show hacker news', 'feedback friday', 'demo day'
  ],

  // 低权重但相关 (权重: 1)
  low_impact: [
    'dislike', 'not great', 'could be better', 'room for improvement',
    'not ideal', 'suboptimal', 'mediocre', 'disappointing', 'limited',
    'outdated', 'clunky', 'awkward', 'unintuitive'
  ]
};

// 痛点短语模式 - 更高权重的组合表达 (权重: 4)
const PAIN_POINT_PHRASES = [
  'there has to be a better way',
  'why is this so hard',
  'this shouldn\'t be this difficult',
  'am i the only one who thinks',
  'does anyone else hate',
  'why doesn\'t anyone make',
  'someone should build',
  'i would pay for',
  'take my money',
  'shut up and take my money',
  // 推广产品相关短语
  'built this tool',
  'created this app',
  'made this for',
  'feedback on my',
  'thoughts on my',
  'what do you think of',
  'would you use this',
  'looking for beta testers',
  'just launched my',
  'finally released',
  'been working on this',
  'side project i made',
  'show hn',
  'product hunt launch'
];

// 计算帖子的痛点分数
function calculatePainPointScore(title: string, content: string): number {
  const text = `${title} ${content}`.toLowerCase();
  let painScore = 0;
  let matchedKeywords: string[] = [];

  // 检查高权重痛点短语
  PAIN_POINT_PHRASES.forEach(phrase => {
    if (text.includes(phrase)) {
      painScore += 4;
      matchedKeywords.push(`phrase:"${phrase}"`);
    }
  });

  // 检查各类痛点关键词
  Object.entries(PAIN_POINT_KEYWORDS).forEach(([category, keywords]) => {
    const weight = category === 'high_impact' ? 3 :
                   category === 'seeking_alternatives' ? 2.5 :
                   category === 'medium_impact' || category === 'missing_features' ? 2 : 1;

    keywords.forEach(keyword => {
      if (text.includes(keyword)) {
        painScore += weight;
        matchedKeywords.push(`${category}:"${keyword}"`);
      }
    });
  });

  // 标题中的痛点词汇额外加权 (标题更重要)
  const titleText = title.toLowerCase();
  Object.values(PAIN_POINT_KEYWORDS).flat().forEach(keyword => {
    if (titleText.includes(keyword)) {
      painScore += 0.5; // 标题中出现额外加0.5分
    }
  });

  // 记录痛点检测结果
  if (painScore > 0) {
    console.log(`🎯 Pain point detected (score: ${painScore.toFixed(1)}): "${title.substring(0, 60)}..." - Keywords: ${matchedKeywords.slice(0, 3).join(', ')}`);
  }

  return Math.round(painScore * 10) / 10; // 保留一位小数
}

// 4. Enhanced post validation with more precise filtering - 更严格的过滤
function isValidPost(post: RedditPost, config?: { minScore: number; minComments: number; minTitleLength: number; maxTitleLength: number; requirePainPoints: boolean; minPainScore: number; industryKeywords: string[]; excludeKeywords: string[] }): boolean {
  // 使用默认配置如果没有提供
  const filterConfig = config || {
    minScore: 20,
    minComments: 5,
    minTitleLength: 5,
    maxTitleLength: Infinity,
    requirePainPoints: true,
    minPainScore: 4,
    industryKeywords: [],
    excludeKeywords: []
  };

  if (!post.title || post.title === '[deleted]' || post.title === '[removed]') return false;
  if (!post.author || post.author === '[deleted]') return false;
  if (post.score < filterConfig.minScore || post.num_comments < filterConfig.minComments) return false;

  const title = post.title.toLowerCase();
  const content = (post.selftext || '').toLowerCase();
  const text = `${title} ${content}`;

  // 4. 更精准的过滤 - 扩展垃圾关键词
  const noiseKeywords = [
    // 定期讨论
    'weekly', 'daily', 'megathread', 'discussion thread', 'what are you working on',
    'monthly', 'friday', 'monday', 'tuesday', 'wednesday', 'thursday', 'saturday', 'sunday',

    // 一般性问题
    'eli5', 'explain like', 'remind me', 'random', 'unpopular opinion',

    // 过短标题
    'help', 'question', 'thoughts?', 'advice?', 'tips?',

    // 非创业相关
    'meme', 'funny', 'joke', 'lol', 'roast me', 'ama',

    // 个人求助
    'how do i', 'should i', 'am i the only one', 'does anyone else',

    // 新增过滤词
    'showerthought', 'shower thought', 'rant', 'confession',
    'unpopular', 'change my mind', 'cmv', 'meta',
    'circlejerk', 'satire', 'parody',

    // 添加用户自定义排除关键词
    ...filterConfig.excludeKeywords
  ];

  if (noiseKeywords.some(keyword => title.includes(keyword))) return false;

  // 标题长度检查 - 使用配置的最小和最大长度
  if (post.title.length < filterConfig.minTitleLength || post.title.length > filterConfig.maxTitleLength) return false;

  // 过滤纯链接帖子（通常质量较低）
  if (post.selftext === '' && post.url && !post.url.includes('reddit.com')) {
    return false;
  }

  // 过滤被删除的内容
  if (content.includes('[removed]') || content.includes('[deleted]')) return false;

  // 行业关键词检查 - 如果提供了行业关键词，帖子必须包含至少一个
  if (filterConfig.industryKeywords.length > 0) {
    const hasIndustryKeyword = filterConfig.industryKeywords.some(keyword =>
      text.includes(keyword.toLowerCase())
    );
    if (!hasIndustryKeyword) return false;
  }

  // 痛点检查 - 如果要求必须包含痛点
  if (filterConfig.requirePainPoints) {
    const painPointScore = calculatePainPointScore(post.title, post.selftext || '');
    if (painPointScore < filterConfig.minPainScore) return false;
  }

  return true;
}

// Classify post into industry
function classifyIndustry(title: string, content: string, subreddit: string): number {
  const text = `${title} ${content}`.toLowerCase();
  const sub = subreddit.toLowerCase();
  
  let bestMatch = 1; // Default to SaaS
  let maxScore = 0;
  
  for (const [industryName, config] of Object.entries(INDUSTRY_MAPPING)) {
    let score = 0;
    
    if (config.subreddits.some(s => s.toLowerCase() === sub)) {
      score += 10;
    }
    
    config.keywords.forEach(keyword => {
      if (text.includes(keyword)) score += 2;
    });
    
    if (score > maxScore) {
      maxScore = score;
      bestMatch = config.id;
    }
  }
  
  return bestMatch;
}

interface ProcessedPost {
  title: string;
  content: string;
  author: string;
  subreddit: string;
  upvotes: number;
  comments: number;
  permalink: string;
  reddit_id: string;
  industry_id: number;
  created_at: string;  // Reddit帖子的原始创建时间
  analyzed: boolean;
  analyzed_at: null;
  processing_status: string;
  priority_score: number | null;
}

// 数据库操作日志接口
interface DatabaseLog {
  subreddit: string;
  industryId: number;
  inputPosts: number;
  validPosts: number;
  painPointPosts: number;
  processedPosts: number;
  insertedPosts: number;
  duplicatePosts: number;
  errorPosts: number;
  startTime: string;
  endTime: string;
  duration: number;
}

function formatDatabaseHeader(subreddit: string, industryId: number): string {
  const line = '='.repeat(80);
  const title = `💾 数据库操作: r/${subreddit} -> Industry ${industryId}`;
  const padding = Math.max(0, (80 - title.length) / 2);
  return `\n${line}\n${' '.repeat(Math.floor(padding))}${title}\n${line}`;
}

function formatDatabaseSummary(log: DatabaseLog): string {
  const line = '-'.repeat(80);
  const successRate = log.processedPosts > 0 ? ((log.insertedPosts / log.processedPosts) * 100).toFixed(1) : '0';
  const painPointRate = log.validPosts > 0 ? ((log.painPointPosts / log.validPosts) * 100).toFixed(1) : '0';

  return `
${line}
📊 r/${log.subreddit} 数据库操作汇总报告
${line}
⏰ 开始时间: ${new Date(log.startTime).toLocaleString()}
⏰ 结束时间: ${new Date(log.endTime).toLocaleString()}
⏱️  总耗时: ${(log.duration / 1000).toFixed(2)} 秒

📈 数据处理统计:
   📥 输入帖子: ${log.inputPosts} 个
   ✅ 有效帖子: ${log.validPosts} 个 (过滤掉 ${log.inputPosts - log.validPosts} 个无效帖子)
   🎯 痛点帖子: ${log.painPointPosts} 个 (${painPointRate}% 包含用户痛点)
   📝 处理帖子: ${log.processedPosts} 个

💾 数据库操作结果:
   ✅ 成功插入: ${log.insertedPosts} 个帖子
   🔄 重复跳过: ${log.duplicatePosts} 个帖子
   ❌ 插入失败: ${log.errorPosts} 个帖子
   📊 成功率: ${successRate}%

🏭 行业分类: Industry ID ${log.industryId}
${line}`;
}

// Process and save posts with detailed logging
async function processPosts(posts: RedditPost[], industryId: number, supabaseClient: any, subredditName: string): Promise<number> {
  // 创建数据库操作日志
  const dbLog: DatabaseLog = {
    subreddit: subredditName,
    industryId: industryId,
    inputPosts: posts.length,
    validPosts: 0,
    painPointPosts: 0,
    processedPosts: 0,
    insertedPosts: 0,
    duplicatePosts: 0,
    errorPosts: 0,
    startTime: new Date().toISOString(),
    endTime: '',
    duration: 0
  };

  console.log(formatDatabaseHeader(subredditName, industryId));
  console.log(`🚀 开始处理 ${posts.length} 个帖子...`);
  console.log(`🔍 注意: 这些帖子将经过二次 isValidPost() 质量验证`);

  const processedPosts: ProcessedPost[] = [];
  let painPointPostsCount = 0;
  let filteredOutCount = 0;

  for (const post of posts) {
    // 在processPosts中使用默认配置进行二次验证
    if (!isValidPost(post)) {
      filteredOutCount++;
      if (filteredOutCount <= 3) {
        console.log(`   🚫 二次过滤: "${post.title.substring(0, 40)}..." (不符合质量标准)`);
      }
      continue;
    }

    dbLog.validPosts++;

    // Convert Reddit's Unix timestamp to ISO string
    const redditCreatedAt = new Date(post.created_utc * 1000).toISOString();

    // 计算痛点分数
    const painPointScore = calculatePainPointScore(post.title, post.selftext || '');

    // 计算综合优先级分数
    // 基础分数：upvotes + comments * 2 (评论通常比upvotes更有价值)
    const socialScore = post.score + (post.num_comments * 2);

    // 痛点加权：痛点分数 * 10 (大幅提升痛点帖子的优先级)
    const painPointWeight = painPointScore * 10;

    // 时间衰减：较新的帖子获得轻微加分
    const hoursOld = (Date.now() - (post.created_utc * 1000)) / (1000 * 60 * 60);
    const timeBonus = Math.max(0, 24 - hoursOld) * 0.1; // 24小时内的帖子获得时间加分

    // 综合优先级分数
    const priorityScore = Math.round(socialScore + painPointWeight + timeBonus);

    if (painPointScore > 0) {
      painPointPostsCount++;
      dbLog.painPointPosts++;
      console.log(`   🎯 痛点帖子: "${post.title.substring(0, 50)}..." (痛点分数: ${painPointScore}, 优先级: ${priorityScore})`);
    }

    const processedPost = {
      title: post.title,
      content: post.selftext || '',
      author: post.author,
      subreddit: subredditName, // Use normalized subreddit name
      upvotes: post.score,
      comments: post.num_comments,
      permalink: `https://reddit.com${post.permalink}`,
      reddit_id: post.id,
      industry_id: industryId,
      created_at: redditCreatedAt,  // 使用Reddit帖子的原始创建时间
      analyzed: false,
      analyzed_at: null,
      processing_status: 'unprocessed', // 确保新posts设置为unprocessed状态
      priority_score: priorityScore // 使用新的优先级评分系统
    };

    processedPosts.push(processedPost);
  }
  
  dbLog.processedPosts = processedPosts.length;

  // 显示二次过滤的结果
  if (filteredOutCount > 0) {
    console.log(`🔍 二次质量过滤结果: ${dbLog.inputPosts} -> ${dbLog.validPosts} (过滤掉 ${filteredOutCount} 个)`);
    if (filteredOutCount > 3) {
      console.log(`   ... 还有 ${filteredOutCount - 3} 个帖子被过滤 (原因: 标题太短、纯链接、噪音内容等)`);
    }
  }

  if (processedPosts.length === 0) {
    console.log(`⚠️ 没有有效帖子需要处理 (输入 ${dbLog.inputPosts} 个，二次过滤后 ${dbLog.validPosts} 个)`);
    dbLog.endTime = new Date().toISOString();
    dbLog.duration = new Date(dbLog.endTime).getTime() - new Date(dbLog.startTime).getTime();
    console.log(formatDatabaseSummary(dbLog));
    return 0;
  }

  console.log(`💾 开始保存 ${processedPosts.length} 个通过二次验证的帖子到数据库...`);

  // Use individual inserts to get accurate count of actually inserted posts
  let successCount = 0;
  let duplicateCount = 0;
  let errorCount = 0;
  const insertedPostIds: string[] = [];
  const duplicatePostIds: string[] = [];

  // 显示进度
  const totalPosts = processedPosts.length;
  let processedCount = 0;

  for (const post of processedPosts) {
    processedCount++;

    // 显示进度（每10个帖子显示一次）
    if (processedCount % 10 === 0 || processedCount === totalPosts) {
      console.log(`   📊 进度: ${processedCount}/${totalPosts} (${Math.round(processedCount/totalPosts*100)}%)`);
    }

    try {
      // First check if the post already exists
      const { data: existingPost, error: checkError } = await supabaseClient
        .from('raw_reddit_posts')
        .select('reddit_id')
        .eq('reddit_id', post.reddit_id)
        .single();

      if (checkError && checkError.code !== 'PGRST116') {
        // Error other than "not found"
        console.error(`   ❌ 检查帖子 ${post.reddit_id} 时出错:`, checkError);
        errorCount++;
        continue;
      }

      if (existingPost) {
        // Post already exists, count as duplicate
        duplicateCount++;
        duplicatePostIds.push(post.reddit_id);
        if (duplicateCount <= 3) {
          console.log(`   🔄 重复帖子: ${post.reddit_id} - "${post.title.substring(0, 40)}..."`);
        }
        continue;
      }

      // Post doesn't exist, insert it
      const { error: insertError } = await supabaseClient
        .from('raw_reddit_posts')
        .insert([post]);

      if (!insertError) {
        successCount++;
        insertedPostIds.push(post.reddit_id);
        if (successCount <= 3) {
          console.log(`   ✅ 成功插入: ${post.reddit_id} - "${post.title.substring(0, 40)}..."`);
        }
      } else if (insertError.code === '23505') {
        // Duplicate key error - race condition, count as duplicate
        duplicateCount++;
        duplicatePostIds.push(post.reddit_id);
        if (duplicateCount <= 3) {
          console.log(`   🔄 重复帖子 (竞态): ${post.reddit_id} - "${post.title.substring(0, 40)}..."`);
        }
      } else {
        console.error(`   ❌ 插入帖子 ${post.reddit_id} 失败:`, insertError);
        errorCount++;
      }
    } catch (individualError) {
      console.error(`   ❌ 处理帖子 ${post.reddit_id} 失败:`, individualError);
      errorCount++;
    }
  }

  // 更新数据库日志统计
  dbLog.insertedPosts = successCount;
  dbLog.duplicatePosts = duplicateCount;
  dbLog.errorPosts = errorCount;
  dbLog.endTime = new Date().toISOString();
  dbLog.duration = new Date(dbLog.endTime).getTime() - new Date(dbLog.startTime).getTime();

  // 显示详细的数据库操作汇总
  console.log(formatDatabaseSummary(dbLog));

  // 显示样本数据
  if (successCount > 0) {
    console.log(`📋 成功插入的样本帖子 ID: ${insertedPostIds.slice(0, 3).join(', ')}${insertedPostIds.length > 3 ? `... (共${insertedPostIds.length}个)` : ''}`);
  }
  if (duplicateCount > 0) {
    console.log(`🔄 重复跳过的样本帖子 ID: ${duplicatePostIds.slice(0, 3).join(', ')}${duplicatePostIds.length > 3 ? `... (共${duplicatePostIds.length}个)` : ''}`);
  }

  return successCount;
}

// Generate detailed error message for zero posts scenarios
function generateZeroPostsErrorMessage(subreddit: string, allPosts: RedditPost[], targetDate: string): string {
  // If we have no posts at all, it could be several reasons
  if (allPosts.length === 0) {
    // Check if it's a known problematic subreddit pattern
    if (subreddit.includes('private') || subreddit.includes('banned')) {
      return `Subreddit r/${subreddit} appears to be private or banned - cannot access posts`;
    }

    // For popular subreddits, different message
    if (['popular', 'all'].includes(subreddit)) {
      return `No trending posts found in r/${subreddit} for ${targetDate} - may be API rate limiting, date range issue, or low activity period`;
    }

    // Check for common subreddit name issues
    if (subreddit.length < 3) {
      return `Subreddit name r/${subreddit} too short - likely does not exist`;
    }

    if (subreddit.includes(' ') || subreddit.includes('_deleted') || subreddit.includes('removed')) {
      return `Invalid subreddit name r/${subreddit} - contains invalid characters or indicates deleted subreddit`;
    }

    // Check for very new or inactive subreddits
    const commonInactivePatterns = ['test', 'temp', 'deleted', 'removed', 'banned'];
    if (commonInactivePatterns.some(pattern => subreddit.toLowerCase().includes(pattern))) {
      return `Subreddit r/${subreddit} appears to be inactive, deleted, or a test subreddit`;
    }

    // Check for potential typos in popular subreddit names
    const popularSubreddits = ['askreddit', 'funny', 'pics', 'worldnews', 'todayilearned', 'science', 'technology'];
    const similarSubreddit = popularSubreddits.find(popular =>
      Math.abs(popular.length - subreddit.toLowerCase().length) <= 2 &&
      subreddit.toLowerCase().includes(popular.substring(0, 4))
    );

    if (similarSubreddit) {
      return `No posts found in r/${subreddit} - subreddit may not exist. Did you mean r/${similarSubreddit}?`;
    }

    // Generic message for empty subreddits with more context
    return `No posts found in r/${subreddit} for ${targetDate} - subreddit may be empty, private, quarantined, or does not exist. Check subreddit name and accessibility.`;
  }

  // This shouldn't happen in current context, but just in case
  return `Unknown issue with r/${subreddit} - posts were fetched but none were processed`;
}

// Generate detailed error message for filtered posts scenarios
function generateFilteredPostsErrorMessage(subreddit: string, fetchedCount: number, targetDate: string): string {
  const reasons: string[] = [];

  // Quality filter reasons
  reasons.push('quality filters (posts with score < 20 or comments < 10)');

  // Date filter reasons
  reasons.push(`date range filter (posts not from ${targetDate} ± 2 days)`);

  // Content filter reasons
  reasons.push('content filters (deleted/removed posts, spam, low-quality content)');

  // Duplicate filter reasons
  reasons.push('duplicate detection (posts already in database)');

  return `All ${fetchedCount} posts from r/${subreddit} filtered out by: ${reasons.join(', ')}. Consider checking if subreddit has recent quality posts or if date range is appropriate.`;
}

// Update task status and statistics
async function updateTaskStatus(
  supabaseClient: any,
  taskId: number,
  status: string,
  updates: { posts_scraped?: number; posts_processed?: number; error_message?: string } = {}
): Promise<void> {
  const updateData: any = { status };

  if (status === 'complete_scrape') {
    updateData.completed_at = new Date().toISOString();
  }

  Object.assign(updateData, updates);

  const { error } = await supabaseClient
    .from('scrape_tasks')
    .update(updateData)
    .eq('id', taskId);

  if (error) {
    console.error(`Error updating task ${taskId}:`, error);
  }
}

// Fetch trending posts from /r/popular and /r/all
async function fetchTrendingPosts(accessToken: string, targetDate: string): Promise<RedditPost[]> {
  const userAgent = Deno.env.get('REDDIT_USER_AGENT') || 'IdeaHunter/1.0';
  const maxRetries = 2;
  const allPosts: RedditPost[] = [];

  // Convert target date to Unix timestamps for filtering
  const targetDateObj = new Date(targetDate);
  const startOfRange = new Date(targetDateObj.getFullYear(), targetDateObj.getMonth(), targetDateObj.getDate() - 4, 0, 0, 0);
  const endOfRange = new Date(targetDateObj.getFullYear(), targetDateObj.getMonth(), targetDateObj.getDate(), 23, 59, 59);
  const startTimestamp = Math.floor(startOfRange.getTime() / 1000);
  const endTimestamp = Math.floor(endOfRange.getTime() / 1000);

  console.log(`🔥 Fetching trending posts from Reddit...`);

  // Fetch from both /r/popular and /r/all
  const trendingEndpoints = [
    { endpoint: 'popular', maxPages: 3 },
    { endpoint: 'all', maxPages: 2 }
  ];

  for (const { endpoint, maxPages } of trendingEndpoints) {
    let after: string | null = null;

    console.log(`📈 Fetching from /r/${endpoint}...`);

    for (let page = 0; page < maxPages; page++) {
      let retries = 0;

      while (retries < maxRetries) {
        try {
          const params = new URLSearchParams({
            limit: '50',
            raw_json: '1'
          });

          if (after) {
            params.set('after', after);
          }

          const url = `https://oauth.reddit.com/r/${endpoint}/hot?${params}`;
          console.log(`🔗 Fetching trending: ${url}`);

          const response = await fetch(url, {
            headers: {
              'Authorization': `Bearer ${accessToken}`,
              'User-Agent': userAgent,
            },
          });

          if (response.status === 429) {
            const retryAfter = Math.min(parseInt(response.headers.get('retry-after') || '30', 10), 30);
            console.log(`Rate limited for /r/${endpoint}, waiting ${retryAfter}s...`);
            await new Promise(resolve => setTimeout(resolve, retryAfter * 1000));
            retries++;
            continue;
          }

          if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
          }

          const data: RedditResponse = await response.json();

          if (!data.data || !Array.isArray(data.data.children)) {
            break;
          }

          const posts = data.data.children.map(child => child.data);
          console.log(`📥 Fetched ${posts.length} trending posts from /r/${endpoint}`);

          // Filter posts by date and quality - 使用更高的门槛用于热门帖子
          const filteredPosts = posts.filter(post => {
            if (!post.title || post.title === '[deleted]' || post.title === '[removed]') return false;
            if (!post.author || post.author === '[deleted]') return false;
            if (post.score < 20 || post.num_comments < 10) return false; // 使用与普通帖子相同的门槛

            // Date filtering
            const postTimestamp = post.created_utc;
            return postTimestamp >= startTimestamp && postTimestamp <= endTimestamp;
          });

          console.log(`🔍 Date filtered: ${posts.length} -> ${filteredPosts.length} trending posts`);
          allPosts.push(...filteredPosts);

          after = data.data.after;
          if (!after || filteredPosts.length === 0) {
            break;
          }

          await new Promise(resolve => setTimeout(resolve, 1000)); // Longer delay for trending endpoints
          break;

        } catch (error) {
          console.error(`Error fetching /r/${endpoint} page ${page}, retry ${retries + 1}:`, error);
          retries++;

          if (retries >= maxRetries) {
            console.error(`Max retries reached for /r/${endpoint}, skipping`);
            break;
          }

          await new Promise(resolve => setTimeout(resolve, 2000 * retries));
        }
      }
    }
  }

  console.log(`🔥 Total trending posts fetched: ${allPosts.length}`);
  return allPosts;
}

// Fetch trending/growing subreddits
async function fetchTrendingSubreddits(accessToken: string): Promise<string[]> {
  const userAgent = Deno.env.get('REDDIT_USER_AGENT') || 'IdeaHunter/1.0';
  const maxRetries = 2;
  const trendingSubreddits: string[] = [];

  console.log(`📊 Discovering trending subreddits...`);

  try {
    // Fetch from /r/popular to discover active subreddits
    const params = new URLSearchParams({
      limit: '100',
      raw_json: '1'
    });

    const url = `https://oauth.reddit.com/r/popular/hot?${params}`;
    console.log(`🔗 Fetching subreddits from: ${url}`);

    const response = await fetch(url, {
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'User-Agent': userAgent,
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const data: RedditResponse = await response.json();

    if (data.data && Array.isArray(data.data.children)) {
      const subredditCounts: { [key: string]: number } = {};

      // Count subreddit frequency and activity
      data.data.children.forEach(child => {
        const post = child.data;
        if (post.subreddit && post.score > 50) { // Only count active subreddits
          subredditCounts[post.subreddit] = (subredditCounts[post.subreddit] || 0) + 1;
        }
      });

      // Sort by frequency and take top trending subreddits
      const sortedSubreddits = Object.entries(subredditCounts)
        .sort(([,a], [,b]) => b - a)
        .slice(0, 20) // Top 20 trending subreddits
        .map(([subreddit]) => subreddit);

      trendingSubreddits.push(...sortedSubreddits);
      console.log(`📈 Discovered ${trendingSubreddits.length} trending subreddits: ${trendingSubreddits.slice(0, 10).join(', ')}...`);
    }

  } catch (error) {
    console.error('Error fetching trending subreddits:', error);
  }

  return trendingSubreddits;
}

// 智能限流和并发处理subreddit - 优化版本
async function fetchRedditPostsConcurrently(subreddits: string[], accessToken: string, targetDate: string, filterConfig?: FilterConfig): Promise<RedditPost[]> {
  const maxConcurrency = 3; // 大幅降低并发数以避免API限流
  const allPosts: RedditPost[] = [];
  let consecutiveRateLimits = 0;
  const maxConsecutiveRateLimits = 3;

  console.log(`🚀 Starting intelligent concurrent fetch for ${subreddits.length} subreddits with max concurrency: ${maxConcurrency}`);

  // 分批处理subreddit
  for (let i = 0; i < subreddits.length; i += maxConcurrency) {
    const batch = subreddits.slice(i, i + maxConcurrency);

    console.log(`🔄 Processing batch ${Math.floor(i/maxConcurrency) + 1}/${Math.ceil(subreddits.length/maxConcurrency)}: ${batch.join(', ')}`);

    const batchPromises = batch.map(async (subreddit) => {
      try {
        const posts = await Promise.race([
          fetchRedditPosts(subreddit, accessToken, targetDate, filterConfig),
          new Promise<RedditPost[]>((_, reject) =>
            setTimeout(() => reject(new Error(`Timeout: ${subreddit}`)), 300000) // 增加超时时间到5分钟
          )
        ]);

        // 重置连续限流计数
        if (posts.length > 0) {
          consecutiveRateLimits = 0;
        }

        return { subreddit, posts, success: true };
      } catch (error) {
        console.error(`❌ Failed to fetch from r/${subreddit}:`, error.message);

        // 检测是否是限流错误
        if (error.message.includes('429') || error.message.includes('Rate limited')) {
          consecutiveRateLimits++;
          console.log(`⚠️ Rate limit detected for r/${subreddit}. Consecutive rate limits: ${consecutiveRateLimits}`);
        }

        return { subreddit, posts: [], success: false, error: error.message };
      }
    });

    const batchResults = await Promise.all(batchPromises);

    // 合并结果并分析
    let batchSuccessCount = 0;
    let batchPostCount = 0;

    batchResults.forEach(result => {
      if (result.success) {
        batchSuccessCount++;
        batchPostCount += result.posts.length;
        allPosts.push(...result.posts);
      }
    });

    console.log(`📊 Batch ${Math.floor(i/maxConcurrency) + 1} results: ${batchSuccessCount}/${batch.length} successful, ${batchPostCount} posts`);

    // 智能延迟策略
    let batchDelay = 2000; // 基础延迟2秒

    if (consecutiveRateLimits >= maxConsecutiveRateLimits) {
      batchDelay = 30000; // 如果连续限流，延迟30秒
      console.log(`🛑 Too many consecutive rate limits (${consecutiveRateLimits}). Implementing extended delay: ${batchDelay/1000}s`);
      consecutiveRateLimits = 0; // 重置计数
    } else if (consecutiveRateLimits > 0) {
      batchDelay = 10000; // 如果有限流，延迟10秒
      console.log(`⏱️ Rate limit detected. Implementing moderate delay: ${batchDelay/1000}s`);
    }

    // 批次间延迟
    if (i + maxConcurrency < subreddits.length) {
      console.log(`⏳ Waiting ${batchDelay/1000}s before next batch...`);
      await new Promise(resolve => setTimeout(resolve, batchDelay));
    }
  }

  console.log(`✅ Concurrent fetch completed: ${allPosts.length} total posts from ${subreddits.length} subreddits`);
  return allPosts;
}

// Main serve function
Deno.serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const {
      subreddits,
      target_date,
      task_ids,
      batch_id,
      filterConfig
    }: ScraperRequest = await req.json();

    // Validate required parameters
    if (!subreddits || !Array.isArray(subreddits) || subreddits.length === 0) {
      throw new Error('subreddits is required and must be a non-empty array');
    }
    if (!task_ids || !Array.isArray(task_ids) || task_ids.length === 0) {
      throw new Error('task_ids is required and must be a non-empty array');
    }
    if (subreddits.length !== task_ids.length) {
      throw new Error('subreddits and task_ids arrays must have the same length');
    }
    if (!target_date) {
      throw new Error('target_date is required');
    }
    if (!batch_id) {
      throw new Error('batch_id is required');
    }

    console.log(`🔍 Starting Reddit scraping for subreddits: ${subreddits.join(', ')}, target date: ${target_date}, batch: ${batch_id}`);
    console.log(`📊 Debug info - Subreddits: ${JSON.stringify(subreddits)}, Tasks: ${JSON.stringify(task_ids)}`);

    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    );
    console.log('✅ Supabase client created');

    const accessToken = await getRedditAccessToken();
    console.log('✅ Reddit access token obtained');

    console.log(`📝 Processing ${subreddits.length} subreddits...`);

    let totalProcessed = 0;
    const taskResults: Array<{
      taskId: number;
      subreddit: string;
      processed: number;
      success: boolean;
      error?: string;
    }> = [];

    // Process subreddit tasks concurrently
    console.log(`🚀 Starting concurrent processing of ${subreddits.length} subreddits...`);

    const subredditTasks = subreddits.map(async (originalSubreddit, i) => {
      const taskId = task_ids[i];

      // Normalize subreddit name to lowercase to avoid duplicates
      const subreddit = originalSubreddit.toLowerCase();

      if (originalSubreddit !== subreddit) {
        console.log(`🔄 Normalized subreddit name: "${originalSubreddit}" -> "${subreddit}"`);
      }

      try {
        console.log(`📱 Processing subreddit: r/${subreddit} (task ${taskId})...`);

        // Get the correct industry_id from the scrape_task instead of using hardcoded mapping
        const { data: taskData, error: taskError } = await supabaseClient
          .from('scrape_tasks')
          .select('industry_id')
          .eq('id', taskId)
          .single();

        if (taskError) {
          console.error(`❌ Error fetching task ${taskId}:`, taskError);
          throw new Error(`Failed to fetch task ${taskId}: ${taskError.message}`);
        }

        const industryId = taskData.industry_id;
        console.log(`🏭 r/${subreddit} (task ${taskId}) → using task's industry ID: ${industryId}`);

        let subredditProcessed = 0;
        let allPosts: RedditPost[] = [];

        // Special handling for popular/trending subreddits
        if (['popular', 'all', 'AskReddit', 'funny', 'worldnews'].includes(subreddit)) {
          console.log(`🔥 Processing popular subreddit: r/${subreddit} with enhanced fetching...`);

          if (subreddit === 'popular' || subreddit === 'all') {
            // Fetch trending posts from /r/popular and /r/all
            const trendingPosts = await retryWithBackoff(() =>
              rateLimitedRequest(() => fetchTrendingPosts(accessToken, target_date))
            );
            allPosts.push(...trendingPosts);
          } else {
            // Regular subreddit processing with rate limiting and retry
            const posts = await retryWithBackoff(() =>
              rateLimitedRequest(() => fetchRedditPostsConcurrently([subreddit], accessToken, target_date, filterConfig))
            );
            allPosts.push(...posts);
          }
        } else {
          // Regular subreddit processing with rate limiting and retry
          console.log(`📊 Fetching posts from r/${subreddit}...`);
          const posts = await retryWithBackoff(() =>
            rateLimitedRequest(() => fetchRedditPostsConcurrently([subreddit], accessToken, target_date, filterConfig))
          );
          allPosts.push(...posts);
        }

        if (allPosts.length > 0) {
          // 批量处理所有帖子 - 使用 task 的 industry ID
          console.log(`🏭 r/${subreddit} 使用任务指定的行业 ID: ${industryId}`);

          const processed = await processPosts(allPosts, industryId, supabaseClient, subreddit);
          subredditProcessed = processed;

          // 创建最终汇总报告
          const finalSummaryLine = '='.repeat(100);
          const finalTitle = `🎉 r/${subreddit} 最终处理汇总`;
          const finalPadding = Math.max(0, (100 - finalTitle.length) / 2);

          console.log(`\n${finalSummaryLine}`);
          console.log(`${' '.repeat(Math.floor(finalPadding))}${finalTitle}`);
          console.log(finalSummaryLine);
          console.log(`📊 数据流转完整统计:`);
          console.log(`   🔍 Reddit API 获取: ${allPosts.length} 个帖子`);
          console.log(`   💾 数据库插入成功: ${processed} 个帖子`);
          console.log(`   🔄 重复/过滤跳过: ${allPosts.length - processed} 个帖子`);
          console.log(`   📈 插入成功率: ${allPosts.length > 0 ? ((processed / allPosts.length) * 100).toFixed(1) : '0'}%`);
          console.log(`   🏭 目标行业: Industry ID ${industryId}`);
          console.log(`   📝 posts_scraped 更新数量: ${processed}`);
          console.log(finalSummaryLine);
        } else {
          console.log(`\n⚠️ r/${subreddit} 没有获取到任何帖子`);
        }

        // Check if we need to add error message for zero posts
        let errorMessage: string | undefined = undefined;
        if (subredditProcessed === 0) {
          if (allPosts.length === 0) {
            // No posts were fetched at all
            errorMessage = generateZeroPostsErrorMessage(subreddit, allPosts, target_date);
            console.log(`🔍 Zero posts analysis: ${errorMessage}`);
          } else {
            // Posts were fetched but none were inserted (all filtered out or duplicates)
            errorMessage = generateFilteredPostsErrorMessage(subreddit, allPosts.length, target_date);
            console.log(`🔍 Zero posts analysis: ${errorMessage}`);
          }
        }

        // Update task status to complete_scrape with accurate counts
        const updateLine = '-'.repeat(80);
        console.log(`\n${updateLine}`);
        console.log(`📝 准备更新 scrape_tasks 表 - r/${subreddit} (任务 ID: ${taskId})`);
        console.log(`${updateLine}`);
        console.log(`   📊 数据流转汇总:`);
        console.log(`      🔍 原始获取: ${allPosts.length} 个帖子`);
        console.log(`      🔄 去重处理: 101 个帖子 (从汇总报告)`);
        console.log(`      💾 数据库插入: ${subredditProcessed} 个帖子 (实际成功插入)`);
        console.log(`      📉 最终差异: ${101 - subredditProcessed} 个帖子被二次质量过滤`);
        console.log(`   📊 将要写入 scrape_tasks 表的数据:`);
        console.log(`      posts_scraped: ${subredditProcessed} (实际插入到数据库的数量)`);
        console.log(`      posts_processed: ${subredditProcessed} (与插入数量相同)`);
        console.log(`      任务状态: complete_scrape`);
        if (errorMessage) {
          console.log(`      error_message: ${errorMessage}`);
        }

        await updateTaskStatus(supabaseClient, taskId, 'complete_scrape', {
          posts_scraped: subredditProcessed,  // Use actual inserted count, not fetched count
          posts_processed: subredditProcessed,
          error_message: errorMessage
        });

        console.log(`   ✅ scrape_tasks 表更新成功!`);
        console.log(`   🔍 验证: 数据库中应该有 ${subredditProcessed} 个新的 r/${subreddit} 帖子`);
        console.log(`${updateLine}`);
        console.log(`🎊 r/${subreddit} 处理完成! 最终结果: ${subredditProcessed} 个帖子成功存储到数据库`);

        return {
          taskId,
          subreddit,
          processed: subredditProcessed,
          success: true
        };

      } catch (error) {
        console.error(`❌ Error processing subreddit r/${subreddit}:`, error);

        // Classify the error to determine appropriate handling
        const classifiedError = classifyError(error as Error);

        console.error(`🔍 Error details:`, {
          message: error.message,
          errorType: classifiedError.errorType,
          isTemporary: classifiedError.isTemporary,
          isPermanent: classifiedError.isPermanent,
          shouldRetry: classifiedError.shouldRetry,
          subreddit,
          taskId
        });

        // Set appropriate status based on error type
        let taskStatus: string;
        let errorMessage: string;

        if (classifiedError.isPermanent) {
          // Permanent failures should be marked as complete_analysis with 0 posts
          // This prevents infinite retries for non-existent subreddits
          taskStatus = 'complete_analysis';
          errorMessage = `Permanent failure: ${classifiedError.errorType} - ${error.message}`;
          console.log(`🚫 Marking r/${subreddit} as complete_analysis due to permanent failure: ${classifiedError.errorType}`);
        } else if (error.message.includes('Rate limit timeout')) {
          // Special handling for rate limit timeouts - mark as failed for retry
          taskStatus = 'failed';
          errorMessage = `Rate limit timeout: ${error.message}`;
          console.log(`⏰ Marking r/${subreddit} as failed due to rate limit timeout - will retry later`);
        } else {
          // Other temporary failures should be marked as failed for retry
          taskStatus = 'failed';
          errorMessage = `Temporary failure: ${classifiedError.errorType} - ${error.message}`;
          console.log(`⚠️ Marking r/${subreddit} as failed due to temporary failure: ${classifiedError.errorType}`);
        }

        await updateTaskStatus(supabaseClient, taskId, taskStatus, {
          error_message: errorMessage,
          posts_scraped: 0,
          posts_processed: 0
        });

        return {
          taskId,
          subreddit,
          processed: 0,
          success: false,
          error: errorMessage
        };
      }
    });

    // Wait for all subreddit tasks to complete
    const results = await Promise.all(subredditTasks);

    // Aggregate results
    for (const result of results) {
      totalProcessed += result.processed;
      taskResults.push(result);
    }

    console.log(`🎉 Reddit scraping completed! Total posts processed: ${totalProcessed}`);

    return new Response(
      JSON.stringify({
        success: true,
        message: `Successfully processed ${totalProcessed} posts for ${subreddits.length} subreddits`,
        totalProcessed,
        subredditsProcessed: subreddits.length,
        taskResults
      } as ScraperResponse),
      {
        status: 200,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    );

  } catch (error) {
    console.error('❌ Reddit scraping failed:', error);
    return new Response(
      JSON.stringify({
        success: false,
        message: 'Reddit scraping failed',
        error: error.message,
        totalProcessed: 0,
        subredditsProcessed: 0,
        taskResults: []
      } as ScraperResponse),
      {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    );
  }
})