import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/lib/queryClient';

export interface BlogPost {
  id: number;
  slug: string;
  title: string;
  excerpt: string;
  content: string;
  author: string;
  published_at: string;
  keywords: string[];
  meta_description: string;
  featured_image_url?: string;
  read_time: number;
  category: string;
  tags: string[];
  view_count: number;
  share_count: number;
}

export interface BlogCategory {
  id: number;
  name: string;
  slug: string;
  description: string;
  color: string;
}

export function useBlogPost(slug: string) {
  return useQuery({
    queryKey: ['blog-post', slug],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('blog_posts')
        .select('*')
        .eq('slug', slug)
        .eq('status', 'published')
        .single();

      if (error) throw error;
      return data as BlogPost;
    },
    enabled: !!slug
  });
}

export function useBlogPosts(category?: string, limit = 10, offset = 0) {
  return useQuery({
    queryKey: ['blog-posts', category, limit, offset],
    queryFn: async () => {
      let query = supabase
        .from('blog_posts')
        .select('*')
        .eq('status', 'published')
        .order('published_at', { ascending: false });

      if (category) {
        query = query.eq('category', category);
      }

      if (limit) {
        query = query.range(offset, offset + limit - 1);
      }

      const { data, error } = await query;
      if (error) throw error;
      return data as BlogPost[];
    }
  });
}

export function useBlogPostsCount(category?: string) {
  return useQuery({
    queryKey: ['blog-posts-count', category],
    queryFn: async () => {
      let query = supabase
        .from('blog_posts')
        .select('*', { count: 'exact', head: true })
        .eq('status', 'published');

      if (category) {
        query = query.eq('category', category);
      }

      const { count, error } = await query;
      if (error) throw error;
      return count || 0;
    }
  });
}

export function useBlogCategories() {
  return useQuery({
    queryKey: ['blog-categories'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('blog_categories')
        .select('*')
        .order('name');

      if (error) throw error;
      return data as BlogCategory[];
    }
  });
}

export function useFeaturedBlogPosts(limit = 3) {
  return useQuery({
    queryKey: ['featured-blog-posts', limit],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('blog_posts')
        .select('*')
        .eq('status', 'published')
        .order('view_count', { ascending: false })
        .limit(limit);

      if (error) throw error;
      return data as BlogPost[];
    }
  });
}

// Function to increment view count
export async function incrementBlogPostViews(slug: string) {
  const { error } = await supabase.rpc('increment_blog_views', { post_slug: slug });
  if (error) console.error('Error incrementing views:', error);
}

export function useSearchBlogPosts(searchQuery: string) {
  return useQuery({
    queryKey: ['search-blog-posts', searchQuery],
    queryFn: async () => {
      if (!searchQuery.trim()) return [];

      const { data, error } = await supabase
        .from('blog_posts')
        .select('*')
        .eq('status', 'published')
        .or(`title.ilike.%${searchQuery}%,content.ilike.%${searchQuery}%,excerpt.ilike.%${searchQuery}%`)
        .order('published_at', { ascending: false });

      if (error) throw error;
      return data as BlogPost[];
    },
    enabled: !!searchQuery.trim()
  });
}

export function useBlogPostsByCategory(category: string, limit = 10) {
  return useQuery({
    queryKey: ['blog-posts-by-category', category, limit],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('blog_posts')
        .select('*')
        .eq('status', 'published')
        .eq('category', category)
        .order('published_at', { ascending: false })
        .limit(limit);

      if (error) throw error;
      return data as BlogPost[];
    },
    enabled: !!category
  });
}

export function useBlogPostsByTag(tag: string, limit = 10) {
  return useQuery({
    queryKey: ['blog-posts-by-tag', tag, limit],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('blog_posts')
        .select('*')
        .eq('status', 'published')
        .contains('tags', [tag])
        .order('published_at', { ascending: false })
        .limit(limit);

      if (error) throw error;
      return data as BlogPost[];
    },
    enabled: !!tag
  });
}

export function useRelatedBlogPosts(currentPostId: number, tags: string[], category: string, limit = 3) {
  return useQuery({
    queryKey: ['related-blog-posts', currentPostId, tags, category, limit],
    queryFn: async () => {
      // First try to find posts with matching tags
      let relatedPosts: BlogPost[] = [];

      if (tags && tags.length > 0) {
        const { data: tagMatches, error: tagError } = await supabase
          .from('blog_posts')
          .select('*')
          .eq('status', 'published')
          .neq('id', currentPostId)
          .overlaps('tags', tags)
          .order('published_at', { ascending: false })
          .limit(limit);

        if (!tagError && tagMatches) {
          relatedPosts = tagMatches as BlogPost[];
        }
      }

      // If we don't have enough posts, fill with posts from the same category
      if (relatedPosts.length < limit && category) {
        const excludeIds = relatedPosts.map(p => p.id).concat([currentPostId]);
        const { data: categoryMatches, error: categoryError } = await supabase
          .from('blog_posts')
          .select('*')
          .eq('status', 'published')
          .eq('category', category)
          .not('id', 'in', `(${excludeIds.join(',')})`)
          .order('published_at', { ascending: false })
          .limit(limit - relatedPosts.length);

        if (!categoryError && categoryMatches) {
          relatedPosts = [...relatedPosts, ...(categoryMatches as BlogPost[])];
        }
      }

      // If we still don't have enough, fill with latest posts
      if (relatedPosts.length < limit) {
        const excludeIds = relatedPosts.map(p => p.id).concat([currentPostId]);
        const { data: latestPosts, error: latestError } = await supabase
          .from('blog_posts')
          .select('*')
          .eq('status', 'published')
          .not('id', 'in', `(${excludeIds.join(',')})`)
          .order('published_at', { ascending: false })
          .limit(limit - relatedPosts.length);

        if (!latestError && latestPosts) {
          relatedPosts = [...relatedPosts, ...(latestPosts as BlogPost[])];
        }
      }

      return relatedPosts.slice(0, limit);
    },
    enabled: !!currentPostId
  });
}
