import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Crown, Sparkles, Zap, Rocket } from 'lucide-react';
import { motion } from 'framer-motion';
import PricingModal from './pricing-modal';
import { usePremiumAccess } from '@/hooks/use-subscription';

interface UpgradeButtonProps {
  variant?: 'default' | 'compact' | 'banner';
  highlightFeature?: 'favorites' | 'timeFilter' | 'allFeatures';
  className?: string;
  children?: React.ReactNode;
}

export default function UpgradeButton({
  variant = 'default',
  highlightFeature,
  className = '',
  children
}: UpgradeButtonProps) {
  const [showPricing, setShowPricing] = useState(false);

  // Always show upgrade button for now - we'll handle pro user logic later

  if (variant === 'compact') {
    return (
      <>
        <Button
          onClick={() => setShowPricing(true)}
          size="sm"
          className={`bg-gradient-to-r from-cyan-500 to-purple-500 hover:from-cyan-600 hover:to-purple-600 text-white font-medium ${className}`}
        >
          <Crown className="w-3 h-3 mr-1" />
          {children || 'Upgrade'}
        </Button>
        <PricingModal
          open={showPricing}
          onOpenChange={setShowPricing}
          highlightFeature={highlightFeature}
        />
      </>
    );
  }

  if (variant === 'banner') {
    return (
      <>
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className={`glass-card rounded-xl p-4 border-2 border-gradient-to-r from-cyan-400 to-purple-400 bg-gradient-to-r from-cyan-500/10 to-purple-500/10 ${className}`}
        >
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="w-12 h-12 bg-gradient-to-r from-cyan-400 to-purple-400 rounded-xl flex items-center justify-center">
                <Rocket className="w-6 h-6 text-white" />
              </div>
              <div>
                <h3 className="text-lg font-bold text-white mb-1">Upgrade to IdeaHunter Pro</h3>
                <p className="text-sm text-gray-300">
                  Unlock unlimited ideas, save favorites, and access all time periods
                </p>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <div className="text-right">
                <div className="flex items-baseline space-x-2">
                  <span className="text-sm text-gray-400 line-through">$9.99</span>
                  <span className="text-xl font-bold text-cyan-400">$4.99/year</span>
                </div>
                <div className="text-xs text-cyan-300 font-medium">50% OFF - Limited Time!</div>
              </div>
              <Button
                onClick={() => setShowPricing(true)}
                className="bg-gradient-to-r from-cyan-500 to-purple-500 hover:from-cyan-600 hover:to-purple-600 text-white font-semibold"
              >
                <Crown className="w-4 h-4 mr-2" />
                Upgrade Now
              </Button>
            </div>
          </div>
        </motion.div>
        <PricingModal
          open={showPricing}
          onOpenChange={setShowPricing}
          highlightFeature={highlightFeature}
        />
      </>
    );
  }

  // Default variant
  return (
    <>
      <motion.div
        whileHover={{ scale: 1.02 }}
        whileTap={{ scale: 0.98 }}
      >
        <Button
          onClick={() => setShowPricing(true)}
          className={`bg-gradient-to-r from-cyan-500 to-purple-500 hover:from-cyan-600 hover:to-purple-600 text-white font-semibold py-3 px-6 ${className}`}
        >
          <Crown className="w-5 h-5 mr-2" />
          {children || 'Upgrade to Pro'}
          <Zap className="w-4 h-4 ml-2" />
        </Button>
      </motion.div>
      <PricingModal
        open={showPricing}
        onOpenChange={setShowPricing}
        highlightFeature={highlightFeature}
      />
    </>
  );
}
