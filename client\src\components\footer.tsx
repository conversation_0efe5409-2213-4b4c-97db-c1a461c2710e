import { Link } from 'wouter';
import { Twitter, Mail, ExternalLink } from 'lucide-react';

export default function Footer() {
  const currentYear = new Date().getFullYear();

  const industryLinks = [
    { name: 'AI & Machine Learning', slug: 'ai-ml' }, // 74 ideas ⭐⭐⭐⭐⭐
    { name: 'Financial Independence', slug: 'financial-independence' }, // 72 ideas ⭐⭐⭐⭐⭐
    { name: 'Family & Parenting', slug: 'family-parenting' }, // 50 ideas ⭐⭐⭐⭐
    { name: 'EdTech', slug: 'edtech' }, // 28 ideas ⭐⭐⭐⭐
    { name: 'Health & Fitness Tech', slug: 'healthtech' }, // 29 ideas ⭐⭐⭐⭐
    { name: 'SaaS & Cloud Services', slug: 'saas-cloud' } // 21 ideas ⭐⭐⭐⭐
  ];

  const resourceLinks = [
    { name: 'Best Startup Ideas 2025', href: '/best/startup-ideas-2025' }, // 综合排行榜 ⭐⭐⭐⭐⭐
    { name: 'Best AI Startup Ideas', href: '/best/ai-startup-ideas-2025' }, // 高搜索量 ⭐⭐⭐⭐⭐
    { name: 'Profitable Business Ideas', href: '/best/profitable-business-ideas' }, // 高搜索量 ⭐⭐⭐⭐⭐
    { name: 'Startup Validation Guide', href: '/faq/how-to-validate-startup-ideas' }, // 高教育价值 ⭐⭐⭐⭐⭐
    { name: 'Trending Ideas', href: '/trends/2025' } // 时效性内容 ⭐⭐⭐⭐
  ];

  return (
    <footer className="bg-gray-900 text-gray-300 border-t border-gray-800 stable-layout">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12 min-h-[400px]">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          
          {/* Company Info */}
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-gradient-to-r from-cyan-500 to-blue-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-sm">IH</span>
              </div>
              <h3 className="text-xl font-bold text-white">IdeaHunter</h3>
            </div>
            <p className="text-sm text-gray-400 leading-relaxed">
              AI-powered platform for discovering trending startup opportunities from Reddit communities. 
              Find your next big idea with data-driven insights.
            </p>
            <div className="flex space-x-4">
              <a 
                href="https://twitter.com/ideahuntertoday" 
                target="_blank" 
                rel="noopener noreferrer"
                className="text-gray-400 hover:text-cyan-400 transition-colors"
                aria-label="Follow us on Twitter"
              >
                <Twitter className="w-5 h-5" />
              </a>

              <a
                href="mailto:<EMAIL>"
                className="text-gray-400 hover:text-cyan-400 transition-colors"
                aria-label="Contact us via email"
              >
                <Mail className="w-5 h-5" />
              </a>
            </div>
          </div>

          {/* Popular Industries */}
          <div className="space-y-4">
            <h4 className="text-lg font-semibold text-white">Popular Industries</h4>
            <ul className="space-y-2">
              {industryLinks.map((industry) => (
                <li key={industry.slug}>
                  <Link 
                    href={`/industry/${industry.slug}`}
                    className="text-sm text-gray-400 hover:text-cyan-400 transition-colors"
                  >
                    {industry.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Resources */}
          <div className="space-y-4">
            <h4 className="text-lg font-semibold text-white">Resources</h4>
            <ul className="space-y-2">
              {resourceLinks.map((link) => (
                <li key={link.href}>
                  <Link 
                    href={link.href}
                    className="text-sm text-gray-400 hover:text-cyan-400 transition-colors"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
              <li>
                <Link
                  href="/alternatives/chatgpt-vs-auramind-ai"
                  className="text-sm text-gray-400 hover:text-cyan-400 transition-colors"
                >
                  ChatGPT vs AI Tools
                </Link>
              </li>
              <li>
                <Link
                  href="/alternatives/shopify-vs-woocommerce"
                  className="text-sm text-gray-400 hover:text-cyan-400 transition-colors"
                >
                  Shopify vs WooCommerce
                </Link>
              </li>

            </ul>
          </div>

          {/* Legal & Support */}
          <div className="space-y-4">
            <h4 className="text-lg font-semibold text-white">Support</h4>
            <ul className="space-y-2">
              <li>
                <Link 
                  href="/about"
                  className="text-sm text-gray-400 hover:text-cyan-400 transition-colors"
                >
                  About Us
                </Link>
              </li>
              <li>
                <Link 
                  href="/privacy"
                  className="text-sm text-gray-400 hover:text-cyan-400 transition-colors"
                >
                  Privacy Policy
                </Link>
              </li>
              <li>
                <Link 
                  href="/terms"
                  className="text-sm text-gray-400 hover:text-cyan-400 transition-colors"
                >
                  Terms of Service
                </Link>
              </li>
              <li>
                <Link 
                  href="/contact"
                  className="text-sm text-gray-400 hover:text-cyan-400 transition-colors"
                >
                  Contact Us
                </Link>
              </li>
              <li>
                <Link
                  href="/faq"
                  className="text-sm text-gray-400 hover:text-cyan-400 transition-colors"
                >
                  FAQ
                </Link>
              </li>
              <li>
                <Link
                  href="/sitemap"
                  className="text-sm text-gray-400 hover:text-cyan-400 transition-colors"
                >
                  Site Map
                </Link>
              </li>
              <li>
                <Link
                  href="/blog"
                  className="text-sm text-gray-400 hover:text-cyan-400 transition-colors"
                >
                  Blog
                </Link>
              </li>
            </ul>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="mt-12 pt-8 border-t border-gray-800">
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            <div className="text-sm text-gray-400">
              © {currentYear} IdeaHunter. All rights reserved. 
              <span className="ml-2">
                Discover startup opportunities from Reddit with AI-powered analysis.
              </span>
            </div>
            <div className="flex items-center space-x-6 text-sm text-gray-400">
              <span>560+ Ideas Analyzed</span>
              <span>35+ Industries</span>
              <span>AI-Powered Insights</span>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}
