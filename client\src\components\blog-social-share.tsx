import React, { useState } from 'react';
import { Share2, Twitter, Facebook, Linkedin, Link2, Check } from 'lucide-react';

interface BlogSocialShareProps {
  url: string;
  title: string;
  description?: string;
  className?: string;
}

export default function BlogSocialShare({ url, title, description, className = '' }: BlogSocialShareProps) {
  const [copied, setCopied] = useState(false);

  const shareData = {
    url: encodeURIComponent(url),
    title: encodeURIComponent(title),
    description: encodeURIComponent(description || title)
  };

  const shareLinks = {
    twitter: `https://twitter.com/intent/tweet?text=${shareData.title}&url=${shareData.url}&hashtags=startup,entrepreneurship,blog`,
    facebook: `https://www.facebook.com/sharer/sharer.php?u=${shareData.url}`,
    linkedin: `https://www.linkedin.com/sharing/share-offsite/?url=${shareData.url}`
  };

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(url);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('Failed to copy URL:', err);
    }
  };

  const openShareWindow = (shareUrl: string) => {
    window.open(
      shareUrl,
      'share-window',
      'width=600,height=400,scrollbars=yes,resizable=yes'
    );
  };

  return (
    <div className={`${className}`}>
      <div className="flex items-center space-x-2">
        <Share2 className="w-4 h-4 text-gray-400" />
        <span className="text-sm text-gray-400 mr-2">Share:</span>
        
        {/* Twitter */}
        <button
          onClick={() => openShareWindow(shareLinks.twitter)}
          className="flex items-center justify-center w-8 h-8 bg-gray-700 hover:bg-blue-600 text-gray-300 hover:text-white rounded-full transition-colors"
          title="Share on Twitter"
        >
          <Twitter className="w-4 h-4" />
        </button>

        {/* Facebook */}
        <button
          onClick={() => openShareWindow(shareLinks.facebook)}
          className="flex items-center justify-center w-8 h-8 bg-gray-700 hover:bg-blue-700 text-gray-300 hover:text-white rounded-full transition-colors"
          title="Share on Facebook"
        >
          <Facebook className="w-4 h-4" />
        </button>

        {/* LinkedIn */}
        <button
          onClick={() => openShareWindow(shareLinks.linkedin)}
          className="flex items-center justify-center w-8 h-8 bg-gray-700 hover:bg-blue-800 text-gray-300 hover:text-white rounded-full transition-colors"
          title="Share on LinkedIn"
        >
          <Linkedin className="w-4 h-4" />
        </button>

        {/* Copy Link */}
        <button
          onClick={copyToClipboard}
          className="flex items-center justify-center w-8 h-8 bg-gray-700 hover:bg-green-600 text-gray-300 hover:text-white rounded-full transition-colors"
          title="Copy link"
        >
          {copied ? <Check className="w-4 h-4" /> : <Link2 className="w-4 h-4" />}
        </button>
      </div>
      
      {copied && (
        <div className="mt-2 text-xs text-green-400">
          Link copied to clipboard!
        </div>
      )}
    </div>
  );
}
