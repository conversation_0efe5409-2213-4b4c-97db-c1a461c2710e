import { Link } from 'wouter';
import { ChevronRight, Home } from 'lucide-react';
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '@/components/ui/breadcrumb';

interface BreadcrumbItem {
  label: string;
  href?: string;
  isCurrentPage?: boolean;
}

interface BreadcrumbNavProps {
  items: BreadcrumbItem[];
  className?: string;
}

export default function BreadcrumbNav({ items, className = '' }: BreadcrumbNavProps) {
  // Always include home as the first item
  const allItems: BreadcrumbItem[] = [
    { label: 'Home', href: '/' },
    ...items
  ];

  return (
    <Breadcrumb className={`mb-4 ${className}`}>
      <BreadcrumbList className="text-gray-400">
        {allItems.map((item, index) => (
          <BreadcrumbItem key={index}>
            {index === 0 && (
              <BreadcrumbLink asChild>
                <Link href={item.href || '/'} className="flex items-center hover:text-cyan-400 transition-colors">
                  <Home className="w-4 h-4" />
                </Link>
              </Link>
            )}
            
            {index > 0 && (
              <>
                {item.isCurrentPage || index === allItems.length - 1 ? (
                  <BreadcrumbPage className="text-white font-medium">
                    {item.label}
                  </BreadcrumbPage>
                ) : (
                  <BreadcrumbLink asChild>
                    <Link href={item.href || '#'} className="hover:text-cyan-400 transition-colors">
                      {item.label}
                    </Link>
                  </BreadcrumbLink>
                )}
              </>
            )}
            
            {index < allItems.length - 1 && (
              <BreadcrumbSeparator>
                <ChevronRight className="w-4 h-4" />
              </BreadcrumbSeparator>
            )}
          </BreadcrumbItem>
        ))}
      </BreadcrumbList>
    </Breadcrumb>
  );
}

// Helper function to generate breadcrumbs for different page types
export function generateBreadcrumbs(pageType: string, data?: any): BreadcrumbItem[] {
  switch (pageType) {
    case 'idea-detail':
      if (data?.idea && data?.industry) {
        return [
          { label: 'Ideas', href: '/dashboard' },
          { label: data.industry.name, href: `/industry/${data.industry.slug}` },
          { label: data.idea.title, isCurrentPage: true }
        ];
      }
      return [
        { label: 'Ideas', href: '/dashboard' },
        { label: 'Idea Details', isCurrentPage: true }
      ];

    case 'industry':
      if (data?.industry) {
        return [
          { label: 'Industries', href: '/dashboard' },
          { label: data.industry.name, isCurrentPage: true }
        ];
      }
      return [
        { label: 'Industries', href: '/dashboard' },
        { label: 'Industry', isCurrentPage: true }
      ];

    case 'best-category':
      if (data?.category) {
        return [
          { label: 'Best Ideas', href: '/best' },
          { label: data.category, isCurrentPage: true }
        ];
      }
      return [
        { label: 'Best Ideas', isCurrentPage: true }
      ];

    case 'alternatives':
      if (data?.comparison) {
        return [
          { label: 'Alternatives', href: '/alternatives' },
          { label: data.comparison, isCurrentPage: true }
        ];
      }
      return [
        { label: 'Alternatives', isCurrentPage: true }
      ];

    case 'faq':
      if (data?.topic) {
        return [
          { label: 'FAQ', href: '/faq' },
          { label: data.topic, isCurrentPage: true }
        ];
      }
      return [
        { label: 'FAQ', isCurrentPage: true }
      ];

    case 'trends':
      if (data?.period) {
        return [
          { label: 'Trends', href: '/trends' },
          { label: data.period, isCurrentPage: true }
        ];
      }
      return [
        { label: 'Trends', isCurrentPage: true }
      ];

    case 'dashboard':
      return [
        { label: 'Dashboard', isCurrentPage: true }
      ];

    default:
      return [];
  }
}
