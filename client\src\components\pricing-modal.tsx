import { useState } from 'react';
import { <PERSON><PERSON>, Dialog<PERSON>ontent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Check, X, Crown, Sparkles, Clock, Heart, Filter, Calendar } from 'lucide-react';
import { motion } from 'framer-motion';
import { useAuth } from '@/hooks/use-auth.tsx';
import AuthModal from './auth-modal';
import PaymentForm from './payment-form';

interface PricingModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  highlightFeature?: 'favorites' | 'timeFilter' | 'allFeatures';
}

export default function PricingModal({ open, onOpenChange, highlightFeature }: PricingModalProps) {
  const { user } = useAuth();
  const [showAuthModal, setShowAuthModal] = useState(false);
  const [showPaymentForm, setShowPaymentForm] = useState(false);

  const handleUpgradeClick = () => {
    if (!user) {
      setShowAuthModal(true);
      return;
    }
    setShowPaymentForm(true);
  };

  const features = [
    {
      id: 'timeFilter',
      icon: Calendar,
      title: 'All Time Periods',
      free: 'Today & Yesterday',
      pro: 'All dates available',
      highlight: highlightFeature === 'timeFilter' || highlightFeature === 'allFeatures'
    },
    {
      id: 'favorites',
      icon: Heart,
      title: 'Favorites System',
      free: 'Not available',
      pro: 'Save unlimited ideas',
      highlight: highlightFeature === 'favorites' || highlightFeature === 'allFeatures'
    },
    {
      id: 'filtering',
      icon: Filter,
      title: 'Advanced Filtering',
      free: 'Not available',
      pro: 'All filters + sorting',
      highlight: highlightFeature === 'allFeatures'
    },
    {
      id: 'access',
      icon: Clock,
      title: 'Access Duration',
      free: 'Not available',
      pro: 'Unlimited access forever',
      highlight: highlightFeature === 'allFeatures'
    }
  ];

  if (showPaymentForm) {
    return (
      <PaymentForm
        open={open}
        onOpenChange={onOpenChange}
        onBack={() => setShowPaymentForm(false)}
      />
    );
  }

  return (
    <>
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="max-w-4xl bg-gray-900 border-gray-700 text-white">
          <DialogHeader>
            <DialogTitle className="text-2xl font-bold text-center mb-6">
              <span className="bg-gradient-to-r from-cyan-400 to-purple-400 bg-clip-text text-transparent">
                Choose Your Plan
              </span>
            </DialogTitle>
          </DialogHeader>

          <div className="grid md:grid-cols-2 gap-6">
            {/* Free Plan */}
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              className="glass-card rounded-xl p-6 border border-gray-600"
            >
              <div className="text-center mb-6">
                <h3 className="text-2xl font-semibold mb-2">Free</h3>
                <div className="text-4xl font-bold mb-2">$0</div>
                <p className="text-gray-400">Access to today & yesterday's ideas</p>
              </div>

              <div className="space-y-3 mb-6">
                {/* All Time Periods */}
                <div className="flex items-center space-x-3 p-3 rounded-lg">
                  <div className="flex-shrink-0">
                    <Calendar className="w-5 h-5 text-gray-400" />
                  </div>
                  <div>
                    <div className="font-medium">All Time Periods</div>
                    <div className="text-sm text-gray-400">Today & Yesterday</div>
                  </div>
                </div>

                {/* Favorites System */}
                <div className="flex items-center space-x-3 p-3 rounded-lg bg-red-500/10 border border-red-500/20">
                  <div className="flex-shrink-0">
                    <X className="w-5 h-5 text-red-400" />
                  </div>
                  <div>
                    <div className="font-medium">Favorites System</div>
                    <div className="text-sm text-red-400">Not available</div>
                  </div>
                </div>
              </div>

              <Button
                variant="outline"
                className="w-full border-gray-600 text-gray-300 hover:bg-gray-800 bg-gray-800"
                disabled
              >
                Current Plan
              </Button>
            </motion.div>

            {/* Pro Plan */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              className="glass-card rounded-xl p-6 border-2 border-cyan-400 relative overflow-hidden"
            >
              {/* Popular Badge */}
              <div className="absolute top-4 right-4">
                <Badge className="bg-gradient-to-r from-cyan-400 to-purple-400 text-black font-semibold">
                  <Crown className="w-3 h-3 mr-1" />
                  POPULAR
                </Badge>
              </div>

              <div className="text-center mb-6">
                <h3 className="text-2xl font-semibold mb-2 flex items-center justify-center">
                  <Sparkles className="w-5 h-5 mr-2 text-cyan-400" />
                  IdeaHunter Pro
                </h3>
                <div className="space-y-1">
                  <div className="text-sm text-gray-400 line-through">$9.99</div>
                  <div className="text-4xl font-bold text-cyan-400">$4.99</div>
                  <div className="text-sm text-cyan-300">50% OFF - Limited Time!</div>
                </div>
                <p className="text-gray-300 mt-2">Annual plan, renews yearly</p>
              </div>

              <div className="space-y-3 mb-6">
                {/* All Time Periods */}
                <div className="flex items-center space-x-3 p-3 rounded-lg bg-cyan-500/10 border border-cyan-500/20">
                  <div className="flex-shrink-0">
                    <Check className="w-5 h-5 text-cyan-400" />
                  </div>
                  <div>
                    <div className="font-medium">All Time Periods</div>
                    <div className="text-sm text-cyan-300">All dates available</div>
                  </div>
                </div>

                {/* Favorites System */}
                <div className="flex items-center space-x-3 p-3 rounded-lg bg-cyan-500/10 border border-cyan-500/20">
                  <div className="flex-shrink-0">
                    <Check className="w-5 h-5 text-cyan-400" />
                  </div>
                  <div>
                    <div className="font-medium">Favorites System</div>
                    <div className="text-sm text-cyan-300">Save unlimited ideas</div>
                  </div>
                </div>

                {/* Advanced Filtering */}
                <div className="flex items-center space-x-3 p-3 rounded-lg bg-cyan-500/10 border border-cyan-500/20">
                  <div className="flex-shrink-0">
                    <Check className="w-5 h-5 text-cyan-400" />
                  </div>
                  <div>
                    <div className="font-medium">Advanced Filtering</div>
                    <div className="text-sm text-cyan-300">All filters + sorting</div>
                  </div>
                </div>

                {/* Access Duration */}
                <div className="flex items-center space-x-3 p-3 rounded-lg bg-cyan-500/10 border border-cyan-500/20">
                  <div className="flex-shrink-0">
                    <Check className="w-5 h-5 text-cyan-400" />
                  </div>
                  <div>
                    <div className="font-medium">Access Duration</div>
                    <div className="text-sm text-cyan-300">Unlimited access forever</div>
                  </div>
                </div>
              </div>

              <Button
                onClick={handleUpgradeClick}
                className="w-full bg-gradient-to-r from-cyan-500 to-purple-500 hover:from-cyan-600 hover:to-purple-600 text-white font-semibold py-3"
              >
                <Crown className="w-4 h-4 mr-2" />
                Upgrade to Pro
              </Button>
            </motion.div>
          </div>

          {/* Bottom message */}
          <div className="mt-6 p-4 bg-gradient-to-r from-pink-500/10 to-red-500/10 rounded-lg border border-pink-500/20">
            <div className="text-center">
              <p className="text-pink-300 font-medium flex items-center justify-center">
                <Heart className="w-4 h-4 mr-2" />
                Unlock the Favorites feature to save your best ideas!
              </p>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      <AuthModal
        open={showAuthModal}
        onOpenChange={setShowAuthModal}
        defaultTab="signup"
      />
    </>
  );
}
