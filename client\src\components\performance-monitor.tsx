import { useEffect } from 'react';

interface PerformanceMetrics {
  fcp: number; // First Contentful Paint
  lcp: number; // Largest Contentful Paint
  fid: number; // First Input Delay
  cls: number; // Cumulative Layout Shift
  ttfb: number; // Time to First Byte
}

// Performance monitoring component
export default function PerformanceMonitor() {
  useEffect(() => {
    // Only run in production
    if (process.env.NODE_ENV !== 'production') return;

    const observer = new PerformanceObserver((list) => {
      const entries = list.getEntries();
      
      entries.forEach((entry) => {
        // Track Core Web Vitals
        if (entry.entryType === 'largest-contentful-paint') {
          // LCP tracked silently
        }

        if (entry.entryType === 'first-input') {
          // FID tracked silently
        }

        if (entry.entryType === 'layout-shift' && !entry.hadRecentInput) {
          // CLS tracked silently
        }
      });
    });

    
    // Observe Core Web Vitals
    observer.observe({ entryTypes: ['largest-contentful-paint', 'first-input', 'layout-shift'] });



    return () => observer.disconnect();
  }, []);

  return null;
}

// Resource preloading utilities
export function preloadCriticalResources() {
  // Only preload resources that actually exist in the public directory
  const criticalResources = [
    { href: '/favicon.svg', as: 'image' },
    // Add other resources here only if they exist
  ];



  // Preload only existing resources
  criticalResources.forEach(resource => {
    const link = document.createElement('link');
    link.rel = 'preload';
    link.as = resource.as;
    link.href = resource.href;
    if (resource.as === 'font') {
      link.crossOrigin = 'anonymous';
    }
    document.head.appendChild(link);
  });

  // Note: Inter font is loaded via system fonts or Google Fonts fallback
  // No need to preload local font files that don't exist
}

// Lazy loading utilities
export function setupLazyLoading() {
  // Lazy load non-critical CSS
  const lazyStyles = [
    '/css/non-critical.css'
  ];

  lazyStyles.forEach(href => {
    const link = document.createElement('link');
    link.rel = 'stylesheet';
    link.href = href;
    link.media = 'print';
    link.onload = function() {
      this.media = 'all';
    };
    document.head.appendChild(link);
  });
}

// Code splitting utilities
export function loadComponentAsync<T>(
  importFn: () => Promise<{ default: T }>,
  fallback?: React.ComponentType
) {
  return React.lazy(async () => {
    try {
      return await importFn();
    } catch (error) {
      console.error('Failed to load component:', error);
      return { default: fallback || (() => null) };
    }
  });
}

// Bundle size optimization
export function optimizeBundle() {
  // Remove unused CSS classes (this would be done at build time)
  // Tree shake unused JavaScript
  // Compress images
  // Minify CSS and JS
  
  // Bundle optimization applied silently
}

// Performance budget monitoring
export function checkPerformanceBudget() {
  if (typeof window === 'undefined') return;

  const budget = {
    maxBundleSize: 250 * 1024, // 250KB
    maxImageSize: 100 * 1024,  // 100KB
    maxFontSize: 50 * 1024,    // 50KB
    maxTTFB: 600,              // 600ms
    maxFCP: 1800,              // 1.8s
    maxLCP: 2500               // 2.5s
  };

  // Check bundle sizes (would be implemented with build tools)
  // Performance budget check completed silently
}

// Critical CSS inlining
export function inlineCriticalCSS() {
  const criticalCSS = `
    /* Critical above-the-fold styles */
    body { margin: 0; font-family: Inter, sans-serif; }
    .header { background: #1f2937; color: white; }
    .loading { display: flex; justify-content: center; align-items: center; }
  `;

  const style = document.createElement('style');
  style.textContent = criticalCSS;
  document.head.appendChild(style);
}

// Service Worker registration for caching
export function registerServiceWorker() {
  if ('serviceWorker' in navigator && process.env.NODE_ENV === 'production') {
    window.addEventListener('load', () => {
      navigator.serviceWorker.register('/sw.js')
        .then((registration) => {
          console.log('SW registered: ', registration);
        })
        .catch((registrationError) => {
          console.log('SW registration failed: ', registrationError);
        });
    });
  }
}

// Resource hints
export function addResourceHints() {
  // DNS prefetch for external domains
  const domains = [
    'fonts.googleapis.com',
    'fonts.gstatic.com',
    'api.ideahunter.today'
  ];

  domains.forEach(domain => {
    const link = document.createElement('link');
    link.rel = 'dns-prefetch';
    link.href = `//${domain}`;
    document.head.appendChild(link);
  });

  // Preconnect to critical origins
  const criticalOrigins = [
    'https://api.ideahunter.today'
  ];

  criticalOrigins.forEach(origin => {
    const link = document.createElement('link');
    link.rel = 'preconnect';
    link.href = origin;
    document.head.appendChild(link);
  });
}

// Initialize all performance optimizations
export function initializePerformanceOptimizations() {
  preloadCriticalResources();
  setupLazyLoading();
  addResourceHints();
  registerServiceWorker();
  
  // Run on next tick to avoid blocking initial render
  setTimeout(() => {
    checkPerformanceBudget();
  }, 0);
}
