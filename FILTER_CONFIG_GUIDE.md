# Reddit Scraper 筛选配置指南

## 🎯 概述

Reddit Scraper 现在支持可配置的筛选参数，允许你根据具体需求动态调整筛选条件，无需修改代码。

## 📋 筛选配置参数

### FilterConfig 接口

```typescript
interface FilterConfig {
  minScore?: number;           // 最小分数门槛 (默认: 20)
  minComments?: number;        // 最小评论数 (默认: 10)
  minTitleLength?: number;     // 最小标题长度 (默认: 5)
  maxTitleLength?: number;     // 最大标题长度 (默认: 无限制)
  pageLimit?: number;          // 每页帖子数量 (默认: 50)
  requirePainPoints?: boolean; // 是否必须包含痛点 (默认: true)
  industryKeywords?: string[]; // 行业相关关键词
  excludeKeywords?: string[];  // 额外排除关键词
  dateRangeDays?: number;      // 日期范围天数 (默认: ±2天)
}
```

### 请求示例

```json
{
  "subreddits": ["saas", "entrepreneur"],
  "target_date": "2024-01-15",
  "task_ids": [1, 2],
  "batch_id": "batch_001",
  "filterConfig": {
    "minScore": 20,
    "minComments": 10,
    "minTitleLength": 5,
    "maxTitleLength": 200,
    "requirePainPoints": true,
    "industryKeywords": ["saas", "software", "startup"],
    "excludeKeywords": ["spam", "test"],
    "dateRangeDays": 2
  }
}
```

## 🔧 默认筛选条件变更

### 新的默认值
- **最小分数**: 20 (之前: 3)
- **最小评论数**: 10 (之前: 1)
- **最小标题长度**: 5 (之前: 20)
- **必须包含痛点**: true (新增)

## 🎯 增强的痛点检测

### 新增痛点类别: 推广现有产品

```typescript
product_promotion: [
  'check out', 'try this', 'recommend', 'built this',
  'created this', 'made this', 'my product', 'our product',
  'launching', 'just released', 'feedback wanted',
  'beta test', 'startup', 'side project', 'mvp'
]
```

### 新增痛点短语

```typescript
// 推广产品相关短语
'built this tool', 'created this app', 'made this for',
'feedback on my', 'just launched my', 'show hn'
```

## 📊 筛选流程

1. **预过滤**: 基本质量检查 (删除/移除的帖子)
2. **分数筛选**: 使用 `minScore` 和 `minComments`
3. **日期筛选**: 使用 `dateRangeDays` 范围
4. **内容质量筛选**: 
   - 标题长度检查
   - 垃圾关键词过滤
   - 自定义排除关键词
5. **行业关键词筛选**: 如果提供了 `industryKeywords`
6. **痛点检测**: 如果 `requirePainPoints` 为 true

## 🚀 使用建议

### 高质量内容筛选
```json
{
  "minScore": 50,
  "minComments": 20,
  "requirePainPoints": true,
  "industryKeywords": ["saas", "b2b", "enterprise"]
}
```

### 宽松筛选 (更多内容)
```json
{
  "minScore": 5,
  "minComments": 2,
  "requirePainPoints": false,
  "dateRangeDays": 5
}
```

### 特定行业筛选
```json
{
  "industryKeywords": ["fintech", "finance", "banking"],
  "excludeKeywords": ["crypto", "bitcoin"],
  "requirePainPoints": true
}
```

## ⚠️ 注意事项

1. **所有参数都是可选的** - 如果不提供，将使用默认值
2. **痛点检测** - 默认开启，确保抓取的内容包含用户痛点或产品推广
3. **行业关键词** - 如果提供，帖子必须包含至少一个关键词
4. **性能影响** - 更严格的筛选可能减少抓取到的帖子数量

## 🔍 调试信息

筛选配置会在日志中显示：
```
📋 筛选配置: 分数>=20, 评论>=10, 标题长度>=5, 必须痛点=true, 日期范围±2天
```
