// Limiter Coordinator <PERSON><PERSON> Job
// This script should be called every 2 minutes to trigger the limiter-coordinator
// Example: Add to your cron job or GitHub Actions workflow

const SUPABASE_URL = process.env.SUPABASE_URL || 'your_supabase_url';
const SUPABASE_SERVICE_ROLE_KEY = process.env.SUPABASE_SERVICE_ROLE_KEY || 'your_service_role_key';

async function triggerLimiterCoordinator() {
  try {
    console.log(`[${new Date().toISOString()}] Triggering limiter-coordinator...`);
    
    const response = await fetch(`${SUPABASE_URL}/functions/v1/limiter-coordinator`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${SUPABASE_SERVICE_ROLE_KEY}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({}), // No parameters needed
    });

    if (response.ok) {
      const result = await response.json();
      console.log(`✅ Limiter coordinator completed successfully:`);
      console.log(`   📅 Dates processed: ${result.dates_processed}`);
      console.log(`   🗑️ Total ideas limited: ${result.total_ideas_limited}`);
      
      if (result.limiter_results && result.limiter_results.length > 0) {
        console.log(`   📊 Results:`);
        result.limiter_results.forEach(r => {
          const status = r.success ? '✅' : '❌';
          console.log(`     ${status} ${r.target_date}: ${r.ideas_deleted} ideas deleted from ${r.industries_processed} industries`);
          if (r.error) {
            console.log(`       Error: ${r.error}`);
          }
        });
      }
    } else {
      const errorText = await response.text();
      console.error(`❌ Limiter coordinator failed: ${response.status} - ${errorText}`);
    }
  } catch (error) {
    console.error(`❌ Error triggering limiter coordinator:`, error.message);
  }
}

// Run the function
triggerLimiterCoordinator();
