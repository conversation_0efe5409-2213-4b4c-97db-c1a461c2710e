// Sitemap generation utilities for SEO

export interface SitemapUrl {
  loc: string;
  lastmod?: string;
  changefreq?: 'always' | 'hourly' | 'daily' | 'weekly' | 'monthly' | 'yearly' | 'never';
  priority?: number;
}

export interface SitemapData {
  urls: SitemapUrl[];
  lastGenerated: string;
}

// Generate sitemap XML
export function generateSitemapXML(urls: SitemapUrl[]): string {
  const urlEntries = urls.map(url => `
  <url>
    <loc>${escapeXml(url.loc)}</loc>
    ${url.lastmod ? `<lastmod>${url.lastmod}</lastmod>` : ''}
    ${url.changefreq ? `<changefreq>${url.changefreq}</changefreq>` : ''}
    ${url.priority !== undefined ? `<priority>${url.priority}</priority>` : ''}
  </url>`).join('');

  return `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
${urlEntries}
</urlset>`;
}

// Escape XML special characters
function escapeXml(unsafe: string): string {
  return unsafe.replace(/[<>&'"]/g, (c) => {
    switch (c) {
      case '<': return '&lt;';
      case '>': return '&gt;';
      case '&': return '&amp;';
      case "'": return '&apos;';
      case '"': return '&quot;';
      default: return c;
    }
  });
}

// Generate sitemap for static pages
export function getStaticPagesSitemap(): SitemapUrl[] {
  const baseUrl = 'https://ideahunter.today';
  const now = new Date().toISOString();

  return [
    {
      loc: baseUrl,
      lastmod: now,
      changefreq: 'daily',
      priority: 1.0
    },
    {
      loc: `${baseUrl}/ideas`,
      lastmod: now,
      changefreq: 'daily',
      priority: 0.9
    },
    {
      loc: `${baseUrl}/blog`,
      lastmod: now,
      changefreq: 'daily',
      priority: 0.9
    },
    {
      loc: `${baseUrl}/alternatives`,
      lastmod: now,
      changefreq: 'weekly',
      priority: 0.8
    },
    {
      loc: `${baseUrl}/best-ideas`,
      lastmod: now,
      changefreq: 'weekly',
      priority: 0.8
    },
    {
      loc: `${baseUrl}/about`,
      lastmod: now,
      changefreq: 'monthly',
      priority: 0.6
    },
    {
      loc: `${baseUrl}/privacy`,
      lastmod: now,
      changefreq: 'yearly',
      priority: 0.3
    },
    {
      loc: `${baseUrl}/terms`,
      lastmod: now,
      changefreq: 'yearly',
      priority: 0.3
    }
  ];
}

// Generate sitemap for blog posts
export async function getBlogPostsSitemap(): Promise<SitemapUrl[]> {
  const baseUrl = 'https://ideahunter.today';
  
  // In a real implementation, this would fetch from Supabase
  // For now, we'll use the known blog posts
  const blogPosts = [
    {
      slug: 'analyzed-560-reddit-startup-ideas-top-15-validated',
      updated_at: '2025-01-15T00:00:00Z'
    },
    {
      slug: 'analyzed-32-industries-reddit-underserved-markets',
      updated_at: '2025-01-15T00:00:00Z'
    },
    {
      slug: 'complete-guide-finding-startup-ideas-reddit-2025',
      updated_at: '2025-01-15T00:00:00Z'
    },
    {
      slug: 'complete-guide-startup-validation-reddit-idea-market-ready',
      updated_at: '2025-01-15T00:00:00Z'
    },
    {
      slug: 'reddit-comments-million-dollar-businesses-success-stories',
      updated_at: '2025-01-15T00:00:00Z'
    },
    {
      slug: 'bootstrapped-startups-reddit-product-market-fit-no-vc',
      updated_at: '2025-01-15T00:00:00Z'
    }
  ];

  return blogPosts.map(post => ({
    loc: `${baseUrl}/blog/${post.slug}`,
    lastmod: post.updated_at,
    changefreq: 'monthly' as const,
    priority: 0.7
  }));
}

// Generate sitemap for startup ideas
export async function getStartupIdeasSitemap(): Promise<SitemapUrl[]> {
  const baseUrl = 'https://ideahunter.today';
  
  // In a real implementation, this would fetch from Supabase
  // For now, we'll generate a sample
  const ideas = Array.from({ length: 100 }, (_, i) => ({
    id: i + 1,
    updated_at: '2025-01-10T00:00:00Z'
  }));

  return ideas.map(idea => ({
    loc: `${baseUrl}/idea/${idea.id}`,
    lastmod: idea.updated_at,
    changefreq: 'weekly' as const,
    priority: 0.6
  }));
}

// Generate sitemap for alternatives pages
export function getAlternativesSitemap(): SitemapUrl[] {
  const baseUrl = 'https://ideahunter.today';
  const now = new Date().toISOString();

  // Known alternatives pages
  const alternatives = [
    'notion', 'airtable', 'monday', 'asana', 'trello', 'clickup',
    'slack', 'discord', 'teams', 'zoom', 'figma', 'sketch',
    'github', 'gitlab', 'bitbucket', 'jira', 'confluence', 'linear'
  ];

  return alternatives.map(tool => ({
    loc: `${baseUrl}/alternatives/${tool}`,
    lastmod: now,
    changefreq: 'monthly' as const,
    priority: 0.5
  }));
}

// Generate sitemap for best ideas pages
export function getBestIdeasSitemap(): SitemapUrl[] {
  const baseUrl = 'https://ideahunter.today';
  const now = new Date().toISOString();

  // Known best ideas pages
  const categories = [
    'ai-tools', 'saas-tools', 'productivity-tools', 'design-tools',
    'developer-tools', 'marketing-tools', 'analytics-tools', 'automation-tools'
  ];

  return categories.map(category => ({
    loc: `${baseUrl}/best-ideas/${category}`,
    lastmod: now,
    changefreq: 'weekly' as const,
    priority: 0.6
  }));
}

// Generate complete sitemap
export async function generateCompleteSitemap(): Promise<SitemapData> {
  const staticPages = getStaticPagesSitemap();
  const blogPosts = await getBlogPostsSitemap();
  const startupIdeas = await getStartupIdeasSitemap();
  const alternatives = getAlternativesSitemap();
  const bestIdeas = getBestIdeasSitemap();

  const allUrls = [
    ...staticPages,
    ...blogPosts,
    ...startupIdeas,
    ...alternatives,
    ...bestIdeas
  ];

  // Sort by priority (highest first) then by URL
  allUrls.sort((a, b) => {
    if (a.priority !== b.priority) {
      return (b.priority || 0) - (a.priority || 0);
    }
    return a.loc.localeCompare(b.loc);
  });

  return {
    urls: allUrls,
    lastGenerated: new Date().toISOString()
  };
}

// Generate robots.txt content
export function generateRobotsTxt(): string {
  const baseUrl = 'https://ideahunter.today';
  
  return `User-agent: *
Allow: /

# Sitemaps
Sitemap: ${baseUrl}/sitemap.xml

# Crawl-delay for respectful crawling
Crawl-delay: 1

# Disallow admin and private areas
Disallow: /admin/
Disallow: /api/
Disallow: /_next/
Disallow: /static/

# Allow important directories
Allow: /blog/
Allow: /ideas/
Allow: /alternatives/
Allow: /best-ideas/

# Block specific user agents if needed
# User-agent: BadBot
# Disallow: /`;
}

// Generate sitemap index for large sites
export function generateSitemapIndex(sitemaps: string[]): string {
  const baseUrl = 'https://ideahunter.today';
  const now = new Date().toISOString();

  const sitemapEntries = sitemaps.map(sitemap => `
  <sitemap>
    <loc>${baseUrl}/${sitemap}</loc>
    <lastmod>${now}</lastmod>
  </sitemap>`).join('');

  return `<?xml version="1.0" encoding="UTF-8"?>
<sitemapindex xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
${sitemapEntries}
</sitemapindex>`;
}

// Validate sitemap URLs
export function validateSitemapUrls(urls: SitemapUrl[]): string[] {
  const errors: string[] = [];

  urls.forEach((url, index) => {
    // Check URL format
    try {
      new URL(url.loc);
    } catch {
      errors.push(`Invalid URL at index ${index}: ${url.loc}`);
    }

    // Check priority range
    if (url.priority !== undefined && (url.priority < 0 || url.priority > 1)) {
      errors.push(`Invalid priority at index ${index}: ${url.priority} (must be 0-1)`);
    }

    // Check lastmod format
    if (url.lastmod && !isValidDate(url.lastmod)) {
      errors.push(`Invalid lastmod at index ${index}: ${url.lastmod}`);
    }
  });

  return errors;
}

// Check if date string is valid ISO format
function isValidDate(dateString: string): boolean {
  const date = new Date(dateString);
  return date instanceof Date && !isNaN(date.getTime());
}

// Submit sitemap to search engines
export async function submitSitemapToSearchEngines(sitemapUrl: string): Promise<void> {
  const searchEngines = [
    `https://www.google.com/ping?sitemap=${encodeURIComponent(sitemapUrl)}`,
    `https://www.bing.com/ping?sitemap=${encodeURIComponent(sitemapUrl)}`
  ];

  const promises = searchEngines.map(async (url) => {
    try {
      const response = await fetch(url, { method: 'GET' });
      console.log(`Submitted sitemap to ${url}: ${response.status}`);
    } catch (error) {
      console.error(`Failed to submit sitemap to ${url}:`, error);
    }
  });

  await Promise.all(promises);
}
