# A string used to distinguish different Supabase projects on the same machine.
# Not necessarily the Project ID in Supabase.
project_id = "ideahunter"

[api]
enabled = true
# Port to use for the API URL.
port = 54321
# Schemas to expose in your API. Tables, views and stored procedures in this schema will get API endpoints.
# public and storage are always included.
schemas = ["public", "graphql_public"]
# Extra schemas to add to the search_path of every request. public is always included.
extra_search_path = ["public", "extensions"]
# The maximum number of rows returned from a table or view without a count.
max_rows = 1000

[db]
# Port to use for the local database URL.
port = 54322
shadow_port = 54320
# External query optimization level.
major_version = 17
root_key = ""

[db.pooler]
enabled = false
pool_mode = "transaction"
default_pool_size = 20
max_client_conn = 100

[realtime]
enabled = true
ip_version = "IPv4"

[studio]
enabled = true
# Port to use for Supabase Studio.
port = 54323
# External URL of the API server that frontend connects to.
api_url = "http://127.0.0.1:54321"

[inbucket]
enabled = true
port = 54324
smtp_port = 54325
pop3_port = 54326

[storage]
enabled = true
# The maximum file size allowed (e.g. "5MB", "500KB").
file_size_limit = "50MiB"

[auth]
enabled = true
# The base URL of your website. Used as an allow-list for redirects and for constructing URLs used
# in emails.
site_url = "http://localhost:5173"
# A list of *exact* URLs that auth providers are permitted to redirect to post authentication.
additional_redirect_urls = ["http://localhost:5173", "https://YOUR_VERCEL_DOMAIN.vercel.app"]
# How long tokens are valid for, in seconds. Defaults to 3600 (1 hour), maximum 604800 (1 week).
jwt_expiry = 3600
# If disabled, the refresh token will never expire.
enable_refresh_token_rotation = true
# Allows refresh tokens to be reused after expiry, up to the specified interval in seconds.
# Requires enable_refresh_token_rotation = true.
refresh_token_reuse_interval = 10
# Allow/disallow new user signups to your project.
enable_signup = true

[auth.external.google]
enabled = true
client_id = "env(GOOGLE_CLIENT_ID)"
secret = "env(GOOGLE_CLIENT_SECRET)"

[auth.email]
# Allow/disallow new user signups via email to your project.
enable_signup = true
# If enabled, a user will be required to confirm any email change on both the old, and new email addresses.
# If disabled, only the new email is required to confirm.
double_confirm_changes = true
# If enabled, users need to confirm their email address before signing in.
enable_confirmations = true
secure_password_change = false
max_frequency = "1m0s"
otp_length = 6
otp_expiry = 3600

[auth.email.template]

[auth.sms]
# Allow/disallow new user signups via SMS to your project.
enable_signup = false
# If enabled, users need to confirm their phone number before signing in.
enable_confirmations = false
# Template for sending a confirmation message when a user signs up via phone.
template = "Your code is {{ .Code }}"
max_frequency = "5s"

[auth.sms.twilio]
enabled = false
account_sid = ""
message_service_sid = ""
auth_token = ""

# Edge Functions configuration
[functions.reddit-scraper]
enabled = true
verify_jwt = true

[functions.deepseek-analyzer]
enabled = true
verify_jwt = true


[functions.analyzer-coordinator]
enabled = true
verify_jwt = true

[functions.scraper-coordinator]
enabled = true
verify_jwt = true

[functions.task-creator]
enabled = true
verify_jwt = true

[functions.industry-idea-limiter]
enabled = true
verify_jwt = true

[functions.limiter-coordinator]
enabled = true
verify_jwt = true

[functions.stripe-webhook]
enabled = true
verify_jwt = false