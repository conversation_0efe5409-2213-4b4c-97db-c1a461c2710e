// Centralized concurrency configuration for all services
// This ensures consistent concurrency limits across the entire system

export const CONCURRENCY_CONFIG = {
  // Maximum number of subreddits that can be processed concurrently
  MAX_CONCURRENT_SUBREDDITS: 3,
  
  // Reddit API rate limiting
  REDDIT_API_RATE_LIMIT: 100, // requests per minute
  REDDIT_API_RATE_WINDOW: 60 * 1000, // 1 minute in milliseconds
  
  // Retry configuration
  MAX_RETRIES: 3,
  BASE_RETRY_DELAY: 1000, // 1 second
  RATE_LIMIT_RETRY_DELAY: 60000, // 1 minute for rate limit errors
  
  // Timeout configuration
  TASK_TIMEOUT: 300000, // 5 minutes
  LOCK_TIMEOUT: 300000, // 5 minutes
  ANALYSIS_TIMEOUT: 600000, // 10 minutes
  
  // Batch processing configuration
  SCRAPER_BATCH_SIZE: 3, // Max subreddits per batch in scraper-coordinator
  ANALYZER_BATCH_SIZE: 3, // Max subreddits per batch in analyzer-coordinator
} as const;

// Service-specific configurations
export const SERVICE_CONFIG = {
  SCRAPER_COORDINATOR: {
    CONCURRENT_SUBREDDITS: CONCURRENCY_CONFIG.MAX_CONCURRENT_SUBREDDITS,
    BATCH_SIZE: CONCURRENCY_CONFIG.SCRAPER_BATCH_SIZE,
    TIMEOUT: CONCURRENCY_CONFIG.TASK_TIMEOUT,
    LOCK_TIMEOUT: CONCURRENCY_CONFIG.LOCK_TIMEOUT,
  },
  
  REDDIT_SCRAPER: {
    MAX_CONCURRENT_SUBREDDITS: CONCURRENCY_CONFIG.MAX_CONCURRENT_SUBREDDITS,
    RATE_LIMIT: CONCURRENCY_CONFIG.REDDIT_API_RATE_LIMIT,
    RATE_WINDOW: CONCURRENCY_CONFIG.REDDIT_API_RATE_WINDOW,
    MAX_RETRIES: CONCURRENCY_CONFIG.MAX_RETRIES,
    BASE_RETRY_DELAY: CONCURRENCY_CONFIG.BASE_RETRY_DELAY,
    RATE_LIMIT_RETRY_DELAY: CONCURRENCY_CONFIG.RATE_LIMIT_RETRY_DELAY,
  },
  
  ANALYZER_COORDINATOR: {
    CONCURRENT_SUBREDDITS: CONCURRENCY_CONFIG.MAX_CONCURRENT_SUBREDDITS,
    BATCH_SIZE: CONCURRENCY_CONFIG.ANALYZER_BATCH_SIZE,
    TIMEOUT: CONCURRENCY_CONFIG.ANALYSIS_TIMEOUT,
    LOCK_TIMEOUT: CONCURRENCY_CONFIG.LOCK_TIMEOUT,
  },
  
  DEEPSEEK_ANALYZER: {
    MAX_CONCURRENT_SUBREDDITS: CONCURRENCY_CONFIG.MAX_CONCURRENT_SUBREDDITS,
    // DeepSeek analyzer processes all subreddits in a batch concurrently
    // No additional rate limiting needed as it's not hitting external APIs directly
  },
} as const;

// Validation function to ensure configuration consistency
export function validateConcurrencyConfig(): void {
  const { SCRAPER_COORDINATOR, ANALYZER_COORDINATOR, REDDIT_SCRAPER } = SERVICE_CONFIG;
  
  // Ensure all services use the same concurrent subreddit limit
  if (
    SCRAPER_COORDINATOR.CONCURRENT_SUBREDDITS !== ANALYZER_COORDINATOR.CONCURRENT_SUBREDDITS ||
    SCRAPER_COORDINATOR.CONCURRENT_SUBREDDITS !== REDDIT_SCRAPER.MAX_CONCURRENT_SUBREDDITS
  ) {
    throw new Error('Inconsistent concurrent subreddit configuration across services');
  }
  
  // Ensure batch sizes don't exceed concurrent limits
  if (
    SCRAPER_COORDINATOR.BATCH_SIZE > SCRAPER_COORDINATOR.CONCURRENT_SUBREDDITS ||
    ANALYZER_COORDINATOR.BATCH_SIZE > ANALYZER_COORDINATOR.CONCURRENT_SUBREDDITS
  ) {
    throw new Error('Batch size cannot exceed concurrent subreddit limit');
  }
  
  console.log('✅ Concurrency configuration validated successfully');
}

// Helper function to get service-specific config
export function getServiceConfig(serviceName: keyof typeof SERVICE_CONFIG) {
  return SERVICE_CONFIG[serviceName];
}

// Export individual configs for backward compatibility
export const SCRAPER_CONFIG = SERVICE_CONFIG.SCRAPER_COORDINATOR;
export const REDDIT_CONFIG = SERVICE_CONFIG.REDDIT_SCRAPER;
export const ANALYZER_CONFIG = SERVICE_CONFIG.ANALYZER_COORDINATOR;
export const DEEPSEEK_CONFIG = SERVICE_CONFIG.DEEPSEEK_ANALYZER;
