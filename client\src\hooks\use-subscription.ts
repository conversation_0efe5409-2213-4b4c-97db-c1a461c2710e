import { useQuery } from '@tanstack/react-query';
import { useAuth } from '@/hooks/use-auth.tsx';
import { supabase } from '@/lib/queryClient';
import type { SubscriptionStatus } from '@/lib/types';

export function useSubscription() {
  const { user, session } = useAuth();

  return useQuery<SubscriptionStatus>({
    queryKey: ['subscription', user?.id],
    queryFn: async () => {
      if (!user || !session) {
        return {
          isPro: false,
          isActive: false,
          canAccessPremiumFeatures: false,
        };
      }

      try {
        const response = await fetch(
          `${import.meta.env.VITE_SUPABASE_URL}/functions/v1/user-subscription`,
          {
            headers: {
              'Authorization': `Bearer ${session.access_token}`,
              'Content-Type': 'application/json',
            },
          }
        );

        if (!response.ok) {
          throw new Error('Failed to fetch subscription status');
        }

        const subscriptionStatus: SubscriptionStatus = await response.json();
        return subscriptionStatus;
      } catch (error) {
        // Return free status on error
        return {
          isPro: false,
          isActive: false,
          canAccessPremiumFeatures: false,
        };
      }
    },
    enabled: !!user && !!session,
    staleTime: 1 * 60 * 1000, // 1 minute
    refetchOnWindowFocus: true,
    retry: 1,
  });
}

// Hook to check if user can access premium features
export function usePremiumAccess() {
  const { data: subscription, isLoading } = useSubscription();
  const { user } = useAuth();

  // For now, assume all users are free users unless we can confirm they're pro
  // This ensures the upgrade prompts work correctly
  const canAccessPremiumFeatures = subscription?.canAccessPremiumFeatures ?? false;

  return {
    canAccessPremiumFeatures,
    isPro: subscription?.isPro ?? false,
    isActive: subscription?.isActive ?? false,
    isLoading,
  };
}

// Hook to check specific feature access
export function useFeatureAccess() {
  const { canAccessPremiumFeatures, isLoading } = usePremiumAccess();
  const { user } = useAuth();

  return {
    canUseFavorites: user ? canAccessPremiumFeatures : false,
    // Free users can access today and yesterday, pro users can access all time ranges
    canAccessAllTimeRanges: user ? canAccessPremiumFeatures : false,
    canAccessTodayAndYesterday: !!user, // All authenticated users can access today and yesterday
    canUseAdvancedFilters: user ? canAccessPremiumFeatures : false,
    isLoading,
  };
}
