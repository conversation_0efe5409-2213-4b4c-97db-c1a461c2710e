import React from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'wouter';
import { ArrowLeft, Calendar, Clock, Eye, Folder } from 'lucide-react';
import SEOHead from '../components/seo-head';
import BlogSearch from '../components/blog-search';
import { useBlogPostsByCategory, useBlogCategories } from '@/hooks/use-blog';
import { formatDistanceToNow } from 'date-fns';

export default function BlogCategory() {
  const { category } = useParams<{ category: string }>();
  const { data: posts, isLoading } = useBlogPostsByCategory(category!);
  const { data: categories } = useBlogCategories();
  
  const currentCategory = categories?.find(cat => cat.slug === category);
  const categoryName = currentCategory?.name || category?.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase());

  return (
    <div className="min-h-screen bg-gray-900 text-white">
      <SEOHead
        title={`${categoryName} Articles | IdeaHunter Blog`}
        description={`Explore ${categoryName} articles about startup ideas, market research, and business opportunities.`}
        keywords={[categoryName?.toLowerCase() || '', "startup blog", "business ideas", "entrepreneurship"]}
        url={`https://ideahunter.today/blog/category/${category}`}
      />
      
      <div className="max-w-6xl mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <Link href="/blog" className="inline-flex items-center text-cyan-400 hover:text-cyan-300 mb-6">
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Blog
          </Link>
          
          <div className="text-center mb-8">
            <div className="flex items-center justify-center mb-4">
              <Folder className="w-8 h-8 mr-3 text-blue-400" />
              <h1 className="text-4xl font-bold">{categoryName}</h1>
            </div>
            {currentCategory?.description && (
              <p className="text-xl text-gray-300 max-w-2xl mx-auto mb-6">
                {currentCategory.description}
              </p>
            )}
            
            {/* Search Bar */}
            <div className="max-w-md mx-auto">
              <BlogSearch />
            </div>
          </div>
        </div>

        {/* Category Stats */}
        <div className="mb-8 text-center">
          <div className="inline-flex items-center bg-gray-800/50 border border-gray-700 rounded-lg px-4 py-2">
            <span className="text-gray-400 mr-2">Articles in this category:</span>
            <span className="text-white font-semibold">{posts?.length || 0}</span>
          </div>
        </div>

        {/* Articles */}
        <section>
          {isLoading ? (
            <div className="space-y-6">
              {[1, 2, 3, 4, 5].map((i) => (
                <div key={i} className="bg-gray-800/50 border border-gray-700 rounded-lg p-6 animate-pulse">
                  <div className="flex justify-between mb-4">
                    <div className="h-6 bg-gray-700 rounded w-20"></div>
                    <div className="h-4 bg-gray-700 rounded w-24"></div>
                  </div>
                  <div className="h-6 bg-gray-700 rounded mb-3"></div>
                  <div className="h-4 bg-gray-700 rounded mb-2"></div>
                  <div className="h-4 bg-gray-700 rounded w-3/4"></div>
                </div>
              ))}
            </div>
          ) : posts && posts.length > 0 ? (
            <div className="space-y-6">
              {posts.map((post) => (
                <article key={post.id} className="bg-gray-800/50 border border-gray-700 rounded-lg p-6 hover:bg-gray-800 hover:border-gray-600 transition-all duration-200">
                  <div className="flex flex-col md:flex-row md:items-center justify-between mb-4">
                    <div className="flex items-center space-x-4 mb-2 md:mb-0">
                      <span className="inline-block bg-gray-700 text-gray-300 text-xs px-2 py-1 rounded">
                        {post.category}
                      </span>
                      <span className="text-sm text-gray-400 flex items-center">
                        <Clock className="w-4 h-4 mr-1" />
                        {post.read_time} min
                      </span>
                      <span className="text-sm text-gray-400 flex items-center">
                        <Eye className="w-4 h-4 mr-1" />
                        {post.view_count}
                      </span>
                    </div>
                    <div className="flex items-center text-sm text-gray-400">
                      <Calendar className="w-4 h-4 mr-1" />
                      {formatDistanceToNow(new Date(post.published_at), { addSuffix: true })}
                    </div>
                  </div>

                  <h3 className="text-xl font-semibold mb-3">
                    <Link
                      href={`/blog/${post.slug}`}
                      className="hover:text-cyan-400 transition-colors"
                    >
                      {post.title}
                    </Link>
                  </h3>

                  <p className="text-gray-300 mb-4">
                    {post.excerpt}
                  </p>

                  {/* Tags */}
                  {post.tags && post.tags.length > 0 && (
                    <div className="flex flex-wrap gap-2">
                      {post.tags.slice(0, 3).map((tag, index) => (
                        <Link
                          key={index}
                          href={`/blog/tag/${tag.toLowerCase().replace(/\s+/g, '-')}`}
                          className="bg-gray-700 text-gray-300 text-xs px-2 py-1 rounded hover:bg-gray-600 transition-colors"
                        >
                          #{tag}
                        </Link>
                      ))}
                      {post.tags.length > 3 && (
                        <span className="text-gray-400 text-xs px-2 py-1">
                          +{post.tags.length - 3} more
                        </span>
                      )}
                    </div>
                  )}
                </article>
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <Folder className="w-16 h-16 mx-auto mb-4 text-gray-600" />
              <h3 className="text-xl font-semibold text-gray-400 mb-2">No articles found</h3>
              <p className="text-gray-500 mb-6">There are no articles in this category yet.</p>
              <Link href="/blog" className="text-blue-400 hover:text-blue-300">
                Browse all articles →
              </Link>
            </div>
          )}
        </section>

        {/* Other Categories */}
        {categories && categories.length > 1 && (
          <section className="mt-12 pt-8 border-t border-gray-700">
            <h2 className="text-2xl font-bold mb-6">Other Categories</h2>
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
              {categories
                .filter(cat => cat.slug !== category)
                .map((cat) => (
                  <Link
                    key={cat.id}
                    href={`/blog/category/${cat.slug}`}
                    className="bg-gray-800/50 border border-gray-700 rounded-lg p-4 hover:bg-gray-800 hover:border-gray-600 transition-all duration-200 text-center"
                  >
                    <div className="text-lg font-semibold text-white mb-1">{cat.name}</div>
                    <div className="text-sm text-gray-400">{cat.description}</div>
                  </Link>
                ))}
            </div>
          </section>
        )}
      </div>
    </div>
  );
}
