import React, { useState } from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'wouter';
import { ArrowLeft, Search, ChevronDown, ChevronUp, Lightbulb, Target, Users, Rocket, TrendingUp } from 'lucide-react';
import SEOHead from '../components/seo-head';

interface FAQItem {
  question: string;
  answer: string;
  category?: string;
}

interface FAQPageConfig {
  title: string;
  description: string;
  keywords: string[];
  icon: string;
  faqs: FAQItem[];
}

const FAQ_CONFIGS: Record<string, FAQPageConfig> = {
  'what-makes-profitable-startup-idea': {
    title: 'What Makes a Profitable Startup Idea? Complete Guide',
    description: 'Learn the key characteristics of profitable startup ideas and how to identify high-potential business opportunities with strong revenue potential.',
    keywords: ['profitable startup idea', 'startup profitability', 'business idea validation', 'startup success factors'],
    icon: '💰',
    faqs: [
      {
        question: "What are the key characteristics of a profitable startup idea?",
        answer: "A profitable startup idea typically has: 1) A clear and sizable target market, 2) A genuine pain point that people are willing to pay to solve, 3) A scalable business model, 4) Reasonable barriers to entry but defensible advantages, 5) Multiple revenue streams potential, and 6) Strong unit economics from early stages.",
        category: "Fundamentals"
      },
      {
        question: "How do I validate if my startup idea will be profitable?",
        answer: "Validate profitability by: 1) Conducting customer interviews to confirm willingness to pay, 2) Analyzing competitor pricing and business models, 3) Creating a minimum viable product (MVP) to test demand, 4) Running pre-sales or landing page tests, 5) Calculating unit economics and lifetime value, and 6) Testing different pricing strategies.",
        category: "Validation"
      },
      {
        question: "What's the difference between a good idea and a profitable idea?",
        answer: "A good idea solves a problem, but a profitable idea solves a problem that people will pay money to solve. Profitable ideas have clear monetization paths, target customers with purchasing power, and address urgent rather than nice-to-have needs. They also consider market timing and competitive landscape.",
        category: "Strategy"
      },
      {
        question: "How important is market size for startup profitability?",
        answer: "Market size is crucial but not everything. A smaller, underserved niche can be more profitable than a large, saturated market. Focus on Total Addressable Market (TAM), Serviceable Addressable Market (SAM), and most importantly, your Serviceable Obtainable Market (SOM). Even 1% of a billion-dollar market can be highly profitable.",
        category: "Market Analysis"
      },
      {
        question: "What are common mistakes that make startup ideas unprofitable?",
        answer: "Common mistakes include: 1) Building solutions without validating the problem, 2) Targeting too broad or too narrow markets, 3) Ignoring unit economics and customer acquisition costs, 4) Underestimating competition and market dynamics, 5) Poor pricing strategy, and 6) Focusing on features rather than customer value.",
        category: "Common Mistakes"
      }
    ]
  },
  'reddit-vs-traditional-market-research': {
    title: 'Reddit vs Traditional Market Research: Which is Better?',
    description: 'Compare Reddit-based market research with traditional methods. Learn when to use each approach for startup idea validation and market analysis.',
    keywords: ['reddit market research', 'traditional market research', 'startup research methods', 'market validation'],
    icon: '📊',
    faqs: [
      {
        question: "How does Reddit compare to traditional market research methods?",
        answer: "Reddit offers real-time, unfiltered insights from actual users discussing genuine problems, while traditional research provides structured, statistically significant data. Reddit is faster and cheaper but less representative, while traditional methods are more comprehensive but slower and expensive.",
        category: "Comparison"
      },
      {
        question: "What are the advantages of using Reddit for market research?",
        answer: "Reddit advantages include: 1) Real, unfiltered conversations about problems, 2) Access to niche communities and early adopters, 3) Cost-effective and fast data collection, 4) Ability to observe natural behavior and pain points, 5) Direct engagement with potential customers, and 6) Trend identification before mainstream adoption.",
        category: "Reddit Benefits"
      },
      {
        question: "What are the limitations of Reddit-based research?",
        answer: "Reddit limitations include: 1) Demographic skew (younger, tech-savvy users), 2) Potential echo chambers and bias, 3) Limited geographic representation, 4) Difficulty in quantifying market size, 5) Informal data that may not represent broader market, and 6) Risk of misinterpreting context or sarcasm.",
        category: "Limitations"
      },
      {
        question: "When should I use traditional market research instead of Reddit?",
        answer: "Use traditional research when: 1) You need statistically significant data, 2) Targeting demographics not well-represented on Reddit, 3) Requiring formal validation for investors, 4) Researching regulated industries, 5) Need precise market sizing, or 6) Developing products for older demographics or specific professional markets.",
        category: "When to Use Traditional"
      },
      {
        question: "How can I combine Reddit and traditional research effectively?",
        answer: "Combine both by: 1) Using Reddit for initial idea generation and problem identification, 2) Following up with traditional surveys for quantification, 3) Using Reddit for qualitative insights and traditional methods for quantitative validation, 4) Leveraging Reddit for trend spotting and traditional research for market sizing, and 5) Using both for comprehensive customer persona development.",
        category: "Best Practices"
      }
    ]
  },
  'ai-tools-for-entrepreneurs': {
    title: 'Best AI Tools for Entrepreneurs: Complete Guide 2025',
    description: 'Discover the most effective AI tools for entrepreneurs to boost productivity, automate tasks, and accelerate business growth in 2025.',
    keywords: ['AI tools entrepreneurs', 'business AI software', 'startup AI tools', 'entrepreneur productivity tools'],
    icon: '🤖',
    faqs: [
      {
        question: "What are the most essential AI tools every entrepreneur should use?",
        answer: "Essential AI tools include: 1) ChatGPT/Claude for content and strategy, 2) Notion AI for documentation and planning, 3) Canva AI for design, 4) Grammarly for writing, 5) Calendly for scheduling, 6) HubSpot for CRM automation, 7) Zapier for workflow automation, and 8) Loom for video communication.",
        category: "Essential Tools"
      },
      {
        question: "How can AI tools help with startup idea validation?",
        answer: "AI tools assist validation through: 1) Market research automation with tools like Perplexity, 2) Competitor analysis using SEMrush AI features, 3) Survey creation and analysis with Typeform AI, 4) Social media sentiment analysis, 5) Trend identification through Google Trends AI, and 6) Customer interview transcription and analysis.",
        category: "Validation"
      },
      {
        question: "What AI tools are best for content marketing and SEO?",
        answer: "Top AI content tools include: 1) Jasper for long-form content, 2) Copy.ai for marketing copy, 3) Surfer SEO for content optimization, 4) Frase for SEO research, 5) Pictory for video content, 6) Buffer AI for social media scheduling, and 7) Ahrefs AI for keyword research and competitor analysis.",
        category: "Marketing"
      },
      {
        question: "How much should entrepreneurs budget for AI tools?",
        answer: "AI tool budgets vary by stage: 1) Early stage: $50-200/month for basic tools, 2) Growth stage: $200-500/month for comprehensive suite, 3) Scale stage: $500-2000/month for enterprise features. Start with free tiers, then upgrade based on ROI. Many tools offer startup discounts through programs like GitHub Student Pack.",
        category: "Budgeting"
      },
      {
        question: "What are the risks of over-relying on AI tools for business?",
        answer: "Risks include: 1) Loss of human creativity and intuition, 2) Dependency on tools that may change or disappear, 3) Generic outputs that lack brand voice, 4) Data privacy and security concerns, 5) Potential for AI hallucinations or errors, and 6) Reduced learning and skill development. Balance AI efficiency with human judgment.",
        category: "Risks"
      }
    ]
  },
  'startup-idea-to-mvp-process': {
    title: 'From Startup Idea to MVP: Step-by-Step Process',
    description: 'Complete guide to transforming your startup idea into a minimum viable product (MVP). Learn the proven process used by successful entrepreneurs.',
    keywords: ['startup idea to MVP', 'MVP development process', 'startup validation', 'product development'],
    icon: '🚀',
    faqs: [
      {
        question: "What are the key steps from startup idea to MVP?",
        answer: "The process includes: 1) Problem validation and market research, 2) Solution hypothesis and feature prioritization, 3) Target customer identification, 4) MVP scope definition and wireframing, 5) Technology stack selection, 6) Development and testing, 7) Launch and user feedback collection, and 8) Iteration based on learnings.",
        category: "Process"
      },
      {
        question: "How do I validate my startup idea before building an MVP?",
        answer: "Validate through: 1) Customer interviews to confirm problem existence, 2) Competitor analysis and market research, 3) Landing page tests to gauge interest, 4) Pre-sales or waitlist building, 5) Prototype testing with potential users, 6) Social media engagement and community feedback, and 7) Expert consultation and mentor advice.",
        category: "Validation"
      },
      {
        question: "What should be included in a minimum viable product?",
        answer: "An MVP should include: 1) Core functionality that solves the main problem, 2) Basic user interface for essential user flows, 3) Minimum features needed for user value, 4) Analytics to track user behavior, 5) Feedback collection mechanisms, and 6) Basic onboarding process. Exclude nice-to-have features and focus on core value proposition.",
        category: "MVP Features"
      },
      {
        question: "How long should it take to build an MVP?",
        answer: "MVP timeline varies by complexity: 1) Simple web app: 4-8 weeks, 2) Mobile app: 8-12 weeks, 3) Complex platform: 12-16 weeks. Factors affecting timeline include team size, technical complexity, feature scope, and available resources. Aim for 2-3 months maximum to maintain momentum and validate quickly.",
        category: "Timeline"
      },
      {
        question: "What are common MVP mistakes to avoid?",
        answer: "Common mistakes include: 1) Building too many features (feature creep), 2) Perfectionism and over-engineering, 3) Skipping user testing and feedback, 4) Ignoring analytics and metrics, 5) Not defining success criteria, 6) Building for everyone instead of specific users, and 7) Spending too much time and money before validation.",
        category: "Common Mistakes"
      }
    ]
  },
  'how-to-find-co-founder': {
    title: 'How to Find the Perfect Co-Founder for Your Startup',
    description: 'Complete guide to finding, evaluating, and partnering with the right co-founder for your startup. Learn proven strategies and avoid common pitfalls.',
    keywords: ['find co-founder', 'startup co-founder', 'co-founder matching', 'startup partnerships'],
    icon: '🤝',
    faqs: [
      {
        question: "Where can I find potential co-founders for my startup?",
        answer: "Find co-founders through: 1) Professional networks and LinkedIn, 2) Startup events and meetups, 3) Co-founder matching platforms like CoFoundersLab, 4) University alumni networks, 5) Industry conferences and hackathons, 6) Online communities (Reddit, Discord), 7) Accelerator programs, and 8) Through mutual connections and referrals.",
        category: "Where to Find"
      },
      {
        question: "What qualities should I look for in a co-founder?",
        answer: "Look for: 1) Complementary skills to yours, 2) Shared vision and values, 3) Strong work ethic and commitment, 4) Good communication and conflict resolution skills, 5) Relevant industry experience, 6) Financial stability for the startup journey, 7) Network and connections, and 8) Emotional resilience and stress management.",
        category: "Qualities"
      },
      {
        question: "How do I evaluate if someone would be a good co-founder?",
        answer: "Evaluate through: 1) Working on a small project together, 2) Discussing vision, goals, and expectations, 3) Checking references and past work, 4) Assessing communication style and conflict handling, 5) Understanding their commitment level and timeline, 6) Reviewing their network and resources, and 7) Testing decision-making compatibility.",
        category: "Evaluation"
      },
      {
        question: "What should be discussed before partnering with a co-founder?",
        answer: "Discuss: 1) Equity split and vesting schedules, 2) Roles, responsibilities, and decision-making authority, 3) Time commitment and availability, 4) Financial contributions and salary expectations, 5) Exit strategies and conflict resolution, 6) Intellectual property ownership, 7) Personal goals and timeline, and 8) Company vision and values alignment.",
        category: "Partnership Terms"
      },
      {
        question: "What are red flags to avoid when choosing a co-founder?",
        answer: "Red flags include: 1) Unwillingness to discuss equity or legal terms, 2) History of unfinished projects or commitments, 3) Poor communication or unavailability, 4) Unrealistic expectations about timeline or success, 5) Lack of relevant skills or experience, 6) Financial instability or unrealistic salary expectations, and 7) Conflicting values or vision for the company.",
        category: "Red Flags"
      }
    ]
  }
};

export default function SpecializedFAQ() {
  const { slug } = useParams();
  const [searchTerm, setSearchTerm] = useState('');
  const [expandedItems, setExpandedItems] = useState<Set<number>>(new Set());

  const config = slug ? FAQ_CONFIGS[slug] : null;

  if (!config) {
    return (
      <div className="min-h-screen bg-gray-900 text-white flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">FAQ Page Not Found</h1>
          <Link href="/faq" className="text-cyan-400 hover:text-cyan-300">
            Return to Main FAQ
          </Link>
        </div>
      </div>
    );
  }

  const filteredFAQs = config.faqs.filter(faq =>
    faq.question.toLowerCase().includes(searchTerm.toLowerCase()) ||
    faq.answer.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const toggleExpanded = (index: number) => {
    const newExpanded = new Set(expandedItems);
    if (newExpanded.has(index)) {
      newExpanded.delete(index);
    } else {
      newExpanded.add(index);
    }
    setExpandedItems(newExpanded);
  };

  return (
    <div className="min-h-screen bg-gray-900 text-white">
      <SEOHead 
        title={config.title}
        description={config.description}
        keywords={config.keywords}
        url={`https://ideahunter.today/faq/${slug}`}
        type="article"
      />
      
      <div className="max-w-4xl mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <Link href="/faq" className="inline-flex items-center text-cyan-400 hover:text-cyan-300 mb-6">
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to FAQ
          </Link>
          
          <div className="text-center">
            <div className="text-6xl mb-4">{config.icon}</div>
            <h1 className="text-4xl font-bold mb-4">{config.title}</h1>
            <p className="text-xl text-gray-300 max-w-2xl mx-auto">
              {config.description}
            </p>
          </div>
        </div>

        {/* Search */}
        <div className="mb-8">
          <div className="relative max-w-md mx-auto">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
            <input
              type="text"
              placeholder="Search questions..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-3 bg-gray-800 border border-gray-700 rounded-lg focus:ring-2 focus:ring-cyan-500 focus:border-transparent"
            />
          </div>
        </div>

        {/* FAQ Items */}
        <div className="space-y-4">
          {filteredFAQs.map((faq, index) => (
            <div key={index} className="bg-gray-800 rounded-lg overflow-hidden">
              <button
                onClick={() => toggleExpanded(index)}
                className="w-full px-6 py-4 text-left hover:bg-gray-750 transition-colors flex items-center justify-between"
              >
                <div className="flex-1">
                  <h3 className="text-lg font-semibold text-white mb-1">
                    {faq.question}
                  </h3>
                  {faq.category && (
                    <span className="text-sm text-cyan-400">{faq.category}</span>
                  )}
                </div>
                {expandedItems.has(index) ? (
                  <ChevronUp className="w-5 h-5 text-gray-400" />
                ) : (
                  <ChevronDown className="w-5 h-5 text-gray-400" />
                )}
              </button>
              
              {expandedItems.has(index) && (
                <div className="px-6 pb-4">
                  <div className="border-t border-gray-700 pt-4">
                    <p className="text-gray-300 leading-relaxed whitespace-pre-line">
                      {faq.answer}
                    </p>
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>

        {filteredFAQs.length === 0 && (
          <div className="text-center py-12">
            <p className="text-gray-400 text-lg">No questions found matching your search.</p>
          </div>
        )}

        {/* Related Links */}
        <div className="mt-12 bg-gradient-to-r from-cyan-600 to-blue-600 rounded-lg p-6">
          <h2 className="text-xl font-bold mb-4">Need More Help?</h2>
          <div className="grid md:grid-cols-2 gap-4">
            <Link 
              href="/faq" 
              className="flex items-center p-3 bg-white/10 rounded-lg hover:bg-white/20 transition-colors"
            >
              <Lightbulb className="w-5 h-5 mr-3" />
              <span>Browse All FAQs</span>
            </Link>
            <Link 
              href="/contact" 
              className="flex items-center p-3 bg-white/10 rounded-lg hover:bg-white/20 transition-colors"
            >
              <Users className="w-5 h-5 mr-3" />
              <span>Contact Support</span>
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
