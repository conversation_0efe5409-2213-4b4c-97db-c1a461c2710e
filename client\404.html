<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
    <title>IdeaHunter - Redirecting...</title>
    <script type="text/javascript">
      // Simple redirect to index.html for SPA routing
      var pathSegmentsToKeep = 1;
      var l = window.location;
      
      // Extract the path after the repo name
      var pathArray = l.pathname.split('/');
      var repoPath = pathArray.slice(0, pathSegmentsToKeep + 1).join('/');
      var routePath = pathArray.slice(pathSegmentsToKeep + 1).join('/');
      
      // Redirect to index.html with the route path as query parameter
      window.location.replace(
        l.protocol + '//' + l.hostname + (l.port ? ':' + l.port : '') +
        repoPath + '/?/' + routePath +
        (l.search ? '&' + l.search.slice(1).replace(/&/g, '~and~') : '') +
        l.hash
      );
    </script>
  </head>
  <body>
    <p>Redirecting...</p>
  </body>
</html> 