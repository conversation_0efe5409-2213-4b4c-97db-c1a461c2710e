import { useState } from 'react';
import { Share2, Twitter, Facebook, Linkedin, Link, Check } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { useToast } from '@/hooks/use-toast';
import { getIdeaShareUrl } from '@/lib/url-utils';
import type { StartupIdea } from '@/lib/types';

interface SocialShareProps {
  idea: StartupIdea;
  className?: string;
}

export default function SocialShare({ idea, className = '' }: SocialShareProps) {
  const [copied, setCopied] = useState(false);
  const { toast } = useToast();

  const shareUrl = getIdeaShareUrl(idea.id, idea.title);

  // Create optimized share text for different platforms
  const createTwitterText = () => {
    const baseText = `Check out this startup idea: ${idea.title}`;
    const hashtags = '#startup #entrepreneurship #ideas';
    const urlLength = 23; // Twitter's t.co URL length
    const hashtagsLength = hashtags.length;

    // Twitter limit is 280 characters, account for URL and hashtags
    const availableLength = 280 - urlLength - hashtagsLength - 3; // 3 for spaces

    if (baseText.length <= availableLength) {
      return baseText;
    }

    // Truncate title if needed
    const prefix = 'Check out this startup idea: ';
    const maxTitleLength = availableLength - prefix.length - 3; // 3 for "..."
    const truncatedTitle = idea.title.substring(0, maxTitleLength) + '...';
    return `${prefix}${truncatedTitle}`;
  };

  const createFacebookText = () => {
    return `Check out this startup idea: ${idea.title}\n\n${idea.summary}`;
  };

  const createLinkedInText = () => {
    return `Check out this startup idea: ${idea.title}\n\n${idea.summary}\n\n#startup #entrepreneurship #ideas`;
  };

  const handleCopyLink = async () => {
    try {
      await navigator.clipboard.writeText(shareUrl);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
      toast({
        title: "Link copied!",
        description: "Share URL has been copied to your clipboard.",
      });
    } catch (err) {
      toast({
        title: "Error",
        description: "Failed to copy link to clipboard.",
        variant: "destructive",
      });
    }
  };

  const handleNativeShare = async () => {
    // Always just copy the link
    handleCopyLink();
  };

  const shareOnTwitter = () => {
    const twitterText = createTwitterText();
    const twitterUrl = `https://twitter.com/intent/tweet?text=${encodeURIComponent(twitterText)}&url=${encodeURIComponent(shareUrl)}&hashtags=startup,entrepreneurship,ideas`;
    window.open(twitterUrl, '_blank', 'width=550,height=420');
  };

  const shareOnFacebook = () => {
    // Use Facebook's modern sharing approach
    // For localhost development, we'll create a shareable text that users can copy
    const isLocalhost = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1';

    if (isLocalhost) {
      // For development: Copy text to clipboard and open Facebook
      const facebookText = createFacebookText();
      const fullText = `${facebookText}\n\n${shareUrl}`;

      // Copy to clipboard
      navigator.clipboard.writeText(fullText).then(() => {
        toast({
          title: "Content copied!",
          description: "Facebook sharing content copied to clipboard. Paste it in the Facebook post.",
        });
      });

      // Open Facebook in new tab
      window.open('https://www.facebook.com/', '_blank');
    } else {
      // For production: Use standard Facebook sharer
      const facebookUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(shareUrl)}`;
      window.open(facebookUrl, '_blank', 'width=550,height=420');
    }
  };

  const shareOnLinkedIn = () => {
    // LinkedIn's modern sharing approach - uses the new feed sharing URL
    // This method works as of 2024 and allows pre-filled text
    const linkedInText = createLinkedInText();
    const linkedinUrl = `https://www.linkedin.com/feed/?shareActive=true&text=${encodeURIComponent(linkedInText + ' ' + shareUrl)}`;
    window.open(linkedinUrl, '_blank', 'width=550,height=420');
  };

  return (
    <Card className={`glass-card border-white/20 bg-transparent rounded-xl p-4 ${className}`}>
      <CardContent className="p-0">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-white flex items-center">
            <Share2 className="w-5 h-5 mr-2" />
            Share This Idea
          </h3>
        </div>
        
        <div className="grid grid-cols-2 sm:grid-cols-4 gap-3">
          {/* Native Share (Mobile) or Copy Link */}
          <Button
            onClick={handleNativeShare}
            className="glass-card rounded-lg px-3 py-4 text-white hover:bg-white/20 transition-all duration-200 border border-white/20 flex flex-col items-center justify-center space-y-2 min-h-[80px]"
          >
            {copied ? <Check className="w-4 h-4 flex-shrink-0" /> : <Link className="w-4 h-4 flex-shrink-0" />}
            <span className="text-xs text-center leading-tight">{copied ? 'Copied!' : 'Copy Link'}</span>
          </Button>

          {/* Twitter */}
          <Button
            onClick={shareOnTwitter}
            className="glass-card rounded-lg px-3 py-4 text-white hover:bg-blue-500/20 hover:border-blue-400/30 transition-all duration-200 border border-white/20 flex flex-col items-center justify-center space-y-2 min-h-[80px]"
          >
            <Twitter className="w-4 h-4 flex-shrink-0" />
            <span className="text-xs text-center leading-tight">Twitter</span>
          </Button>

          {/* Facebook */}
          <Button
            onClick={shareOnFacebook}
            className="glass-card rounded-lg px-3 py-4 text-white hover:bg-blue-600/20 hover:border-blue-500/30 transition-all duration-200 border border-white/20 flex flex-col items-center justify-center space-y-2 min-h-[80px]"
          >
            <Facebook className="w-4 h-4 flex-shrink-0" />
            <span className="text-xs text-center leading-tight">Facebook</span>
          </Button>

          {/* LinkedIn */}
          <Button
            onClick={shareOnLinkedIn}
            className="glass-card rounded-lg px-3 py-4 text-white hover:bg-blue-700/20 hover:border-blue-600/30 transition-all duration-200 border border-white/20 flex flex-col items-center justify-center space-y-2 min-h-[80px]"
          >
            <Linkedin className="w-4 h-4 flex-shrink-0" />
            <span className="text-xs text-center leading-tight">LinkedIn</span>
          </Button>
        </div>

        <div className="mt-4 p-3 bg-white/5 rounded-lg border border-white/10">
          <p className="text-gray-400 text-xs mb-2">Share URL:</p>
          <p className="text-gray-300 text-sm font-mono break-all">{shareUrl}</p>
        </div>
      </CardContent>
    </Card>
  );
}
