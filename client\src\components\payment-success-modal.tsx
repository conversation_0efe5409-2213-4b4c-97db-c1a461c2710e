import { motion } from 'framer-motion';
import { Dialog, DialogContent, DialogHeader } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { CheckCircle, Crown, Heart, Calendar, Filter, Sparkles } from 'lucide-react';

interface PaymentSuccessModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  amount?: string;
}

export default function PaymentSuccessModal({ open, onOpenChange, amount }: PaymentSuccessModalProps) {
  const features = [
    {
      icon: Calendar,
      title: 'All Time Periods',
      description: 'Access ideas from any date'
    },
    {
      icon: Heart,
      title: 'Unlimited Favorites',
      description: 'Save your best startup ideas'
    },
    {
      icon: Filter,
      title: 'Advanced Filtering',
      description: 'Use all filters and sorting'
    }
  ];

  const handleContinue = () => {
    onOpenChange(false);
    // Refresh the page to update user subscription status
    window.location.reload();
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl bg-gray-900 border-gray-700 text-white">
        <DialogHeader className="sr-only">
          <span>Payment Successful</span>
        </DialogHeader>

        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.6 }}
          className="text-center py-6"
        >
          {/* Success Icon */}
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
            className="mb-6"
          >
            <div className="relative inline-block">
              <div className="w-20 h-20 bg-gradient-to-r from-green-400 to-cyan-400 rounded-full flex items-center justify-center mx-auto">
                <CheckCircle className="w-10 h-10 text-white" />
              </div>
              <motion.div
                initial={{ scale: 0, rotate: 0 }}
                animate={{ scale: 1, rotate: 360 }}
                transition={{ delay: 0.5, duration: 0.8 }}
                className="absolute -top-1 -right-1"
              >
                <Crown className="w-6 h-6 text-yellow-400" />
              </motion.div>
            </div>
          </motion.div>

          {/* Success Message */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
            className="mb-6"
          >
            <h1 className="text-3xl font-bold mb-3">
              <span className="bg-gradient-to-r from-green-400 to-cyan-400 bg-clip-text text-transparent">
                Payment Successful! 🎉
              </span>
            </h1>
            <p className="text-lg text-gray-300 mb-2">
              Welcome to ScraperDash Pro!
            </p>
            {amount && (
              <p className="text-sm text-gray-400">
                Payment of ${amount} processed successfully
              </p>
            )}
          </motion.div>

          {/* Features Unlocked */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.6 }}
            className="mb-6"
          >
            <div className="flex items-center justify-center gap-2 mb-4">
              <Sparkles className="w-5 h-5 text-cyan-400" />
              <h2 className="text-xl font-semibold text-cyan-300">
                Features Unlocked
              </h2>
              <Sparkles className="w-5 h-5 text-cyan-400" />
            </div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
              {features.map((feature, index) => {
                const Icon = feature.icon;
                return (
                  <motion.div
                    key={feature.title}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.8 + index * 0.1 }}
                  >
                    <Card className="glass-card border-cyan-500/30 bg-cyan-500/10">
                      <CardContent className="p-3 text-center">
                        <div className="w-10 h-10 bg-gradient-to-r from-cyan-500 to-purple-500 rounded-lg flex items-center justify-center mx-auto mb-2">
                          <Icon className="w-5 h-5 text-white" />
                        </div>
                        <h3 className="font-semibold text-white text-sm mb-1">{feature.title}</h3>
                        <p className="text-xs text-gray-300">{feature.description}</p>
                      </CardContent>
                    </Card>
                  </motion.div>
                );
              })}
            </div>
          </motion.div>

          {/* Action Button */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 1.2 }}
          >
            <Button
              onClick={handleContinue}
              className="bg-gradient-to-r from-cyan-500 to-purple-500 hover:from-cyan-600 hover:to-purple-600 text-white font-semibold py-3 px-8 text-lg"
            >
              <Crown className="w-5 h-5 mr-2" />
              Start Exploring Pro Features
            </Button>
          </motion.div>

          {/* Thank You Message */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 1.5 }}
            className="mt-6 p-4 glass-card rounded-lg border border-cyan-500/30"
          >
            <p className="text-cyan-300 font-medium text-sm">
              Thank you for supporting ScraperDash! 💙
            </p>
            <p className="text-xs text-gray-400 mt-1">
              Your support helps us continue to find and analyze the best startup ideas
            </p>
          </motion.div>
        </motion.div>
      </DialogContent>
    </Dialog>
  );
}
