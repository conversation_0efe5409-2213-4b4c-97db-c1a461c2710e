// 测试新的筛选配置功能
const SUPABASE_URL = 'https://your-project.supabase.co';
const SUPABASE_ANON_KEY = 'your-anon-key';

async function testFilterConfig() {
  try {
    // 获取昨天的日期
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    const targetDate = yesterday.toISOString().split('T')[0];
    
    console.log(`Testing filter config with target date: ${targetDate}`);
    
    // 测试请求体，包含新的筛选配置
    const testPayload = {
      subreddits: ['saas'],
      target_date: targetDate,
      task_ids: [999], // 测试用的task ID
      batch_id: 'test_filter_config',
      filterConfig: {
        minScore: 20,           // 最小分数20
        minComments: 10,        // 最小评论数10
        minTitleLength: 5,      // 最小标题长度5
        maxTitleLength: 200,    // 最大标题长度200
        requirePainPoints: true, // 必须包含痛点
        minPainScore: 4,        // 最小痛点分数4
        industryKeywords: ['saas', 'software', 'startup', 'business'], // 行业关键词
        excludeKeywords: ['spam', 'test'], // 排除关键词
        dateRangeDays: 2        // 日期范围±2天
      }
    };
    
    console.log('Test payload:', JSON.stringify(testPayload, null, 2));
    
    // 注意：这只是一个测试脚本示例，实际使用时需要：
    // 1. 替换正确的 SUPABASE_URL 和 SUPABASE_ANON_KEY
    // 2. 确保有有效的 task_ids
    // 3. 在实际环境中运行
    
    console.log('✅ Filter config test payload created successfully!');
    console.log('📋 New filtering features:');
    console.log('  - Configurable score threshold (default: 20)');
    console.log('  - Configurable comment threshold (default: 10)');
    console.log('  - Configurable title length (default: 5-∞)');
    console.log('  - Required pain points (default: true)');
    console.log('  - Minimum pain score threshold (default: 4)');
    console.log('  - Industry keywords filtering');
    console.log('  - Custom exclude keywords');
    console.log('  - Configurable date range (default: ±2 days)');
    console.log('  - Enhanced pain point detection including product promotion');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

// 运行测试
testFilterConfig();
