import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/lib/queryClient';
import type { Industry } from '@/lib/types';

export function useIndustries() {
  return useQuery<Industry[]>({
    queryKey: ['industries'],
    queryFn: async () => {
      // Use parallel queries instead of N+1 queries to optimize performance
      // This reduces 39 individual API calls to just 2 parallel calls
      const [industriesResult, ideaCountsResult] = await Promise.all([
        // Get all industries
        supabase
          .from('industries')
          .select('*')
          .order('id'),

        // Get all startup ideas' industry_ids for counting
        supabase
          .from('startup_ideas')
          .select('industry_id')
      ]);

      if (industriesResult.error) {
        throw new Error(`Failed to fetch industries: ${industriesResult.error.message}`);
      }

      if (ideaCountsResult.error) {
        throw new Error(`Failed to fetch idea counts: ${ideaCountsResult.error.message}`);
      }

      const industries = industriesResult.data || [];
      const ideaCounts = ideaCountsResult.data || [];

      // Create a count map for efficient lookup
      const countMap = ideaCounts.reduce((acc, idea) => {
        acc[idea.industry_id] = (acc[idea.industry_id] || 0) + 1;
        return acc;
      }, {} as Record<number, number>);

      // Merge industries with their idea counts
      const industriesWithCounts = industries.map(industry => ({
        ...industry,
        ideaCount: countMap[industry.id] || 0
      }));

      return industriesWithCounts;
    },
  });
}
