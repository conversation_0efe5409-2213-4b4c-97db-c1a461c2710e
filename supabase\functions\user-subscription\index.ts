import "jsr:@supabase/functions-js/edge-runtime.d.ts";
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

interface SubscriptionStatus {
  isPro: boolean;
  isActive: boolean;
  expiresAt?: string;
  canAccessPremiumFeatures: boolean;
}

Deno.serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    // Initialize Supabase client
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    );

    // Get user from JWT token
    const authHeader = req.headers.get('Authorization');
    if (!authHeader) {
      throw new Error('No authorization header');
    }

    const token = authHeader.replace('Bearer ', '');
    const { data: { user }, error: userError } = await supabaseClient.auth.getUser(token);

    if (userError || !user) {
      throw new Error('Invalid user token');
    }

    console.log(`Checking subscription for user: ${user.id}`);

    // Get user profile with subscription info
    const { data: profile, error: profileError } = await supabaseClient
      .from('user_profiles')
      .select('subscription_status, subscription_expires_at')
      .eq('id', user.id)
      .single();

    if (profileError) {
      console.error('Error fetching user profile:', profileError);
      // If profile doesn't exist, create it
      if (profileError.code === 'PGRST116') {
        await supabaseClient
          .from('user_profiles')
          .insert({
            id: user.id,
            email: user.email || '',
            subscription_status: 'free',
          });
      }
    }

    // Determine subscription status
    const subscriptionStatus = profile?.subscription_status || 'free';
    const expiresAt = profile?.subscription_expires_at;
    
    // Check if subscription is still active
    let isActive = false;
    let isPro = false;
    
    if (subscriptionStatus === 'pro') {
      isPro = true;
      if (expiresAt) {
        const expirationDate = new Date(expiresAt);
        const now = new Date();
        isActive = expirationDate > now;
        
        // If subscription has expired, update status
        if (!isActive) {
          await supabaseClient
            .from('user_profiles')
            .update({
              subscription_status: 'free',
              updated_at: new Date().toISOString(),
            })
            .eq('id', user.id);
          isPro = false;
        }
      } else {
        // If no expiration date, assume active (shouldn't happen in normal flow)
        isActive = true;
      }
    }

    const response: SubscriptionStatus = {
      isPro,
      isActive,
      expiresAt: isActive ? expiresAt : null,
      canAccessPremiumFeatures: isPro && isActive,
    };

    console.log(`Subscription status for ${user.id}:`, response);

    return new Response(
      JSON.stringify(response),
      {
        status: 200,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      }
    );
  } catch (error) {
    console.error('Subscription check error:', error);
    
    // Return free status on error
    const fallbackResponse: SubscriptionStatus = {
      isPro: false,
      isActive: false,
      canAccessPremiumFeatures: false,
    };

    return new Response(
      JSON.stringify(fallbackResponse),
      {
        status: 200,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      }
    );
  }
});
