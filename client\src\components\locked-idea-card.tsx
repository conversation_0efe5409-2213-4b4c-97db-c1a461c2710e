import { Lock, Crown, ArrowUp, MessageSquare, Calendar } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { motion } from "framer-motion";
import { useState } from "react";
import { formatTargetDate } from "@/lib/utils";
import { getIndustryColor } from "@/lib/industry-colors";
import PricingModal from "./pricing-modal";
import type { StartupIdea } from "@/lib/types";

interface LockedIdeaCardProps {
  idea: StartupIdea;
  index: number;
}

export default function LockedIdeaCard({ idea, index }: LockedIdeaCardProps) {
  const [pricingModalOpen, setPricingModalOpen] = useState(false);

  const industryColor = getIndustryColor(idea.industry?.name || '');

  return (
    <>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: index * 0.1 }}
        className="relative"
      >
        <Card className="glass-card border-white/10 hover:border-white/20 transition-all duration-200 relative overflow-hidden">
          {/* Overlay for locked state */}
          <div className="absolute inset-0 bg-black/60 backdrop-blur-sm z-10 flex items-center justify-center">
            <div className="text-center p-6">
              <Lock className="w-8 h-8 text-gray-400 mx-auto mb-3" />
              <h3 className="text-lg font-semibold text-white mb-2">Premium Content</h3>
              <p className="text-gray-400 text-sm mb-4">
                Upgrade to Pro to access all startup ideas
              </p>
              <Button
                onClick={() => setPricingModalOpen(true)}
                className="bg-gradient-to-r from-cyan-500 to-purple-500 hover:from-cyan-600 hover:to-purple-600 text-white font-semibold px-4 py-2 text-sm"
              >
                <Crown className="w-4 h-4 mr-2" />
                Upgrade Now
              </Button>
            </div>
          </div>

          {/* Blurred content behind overlay */}
          <CardContent className="p-6 filter blur-sm">
            <div className="flex items-start justify-between mb-4">
              <div className="flex-1">
                <h3 className="text-lg font-semibold text-white mb-2 line-clamp-2">
                  {idea.title}
                </h3>
                <p className="text-gray-400 text-sm line-clamp-3 mb-3">
                  {idea.summary}
                </p>
              </div>
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4 text-sm text-gray-400">
                <div className="flex items-center">
                  <ArrowUp className="w-4 h-4 mr-1" />
                  <span>{idea.upvotes}</span>
                </div>
                <div className="flex items-center">
                  <MessageSquare className="w-4 h-4 mr-1" />
                  <span>{idea.comments}</span>
                </div>
                <div className="flex items-center">
                  <Calendar className="w-4 h-4 mr-1" />
                  <span>{formatTargetDate(idea.target_date)}</span>
                </div>
              </div>
            </div>

            <div className="flex items-center justify-between mt-4">
              {idea.industry && (
                <Badge 
                  variant="secondary" 
                  className={`${industryColor} text-white border-0`}
                >
                  {idea.industry.name}
                </Badge>
              )}
              <div className="flex items-center space-x-2">
                <Badge variant="outline" className="border-yellow-500/30 text-yellow-400">
                  Score: {idea.confidence_score}
                </Badge>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>

      <PricingModal
        open={pricingModalOpen}
        onOpenChange={setPricingModalOpen}
        highlightFeature="allFeatures"
      />
    </>
  );
}
